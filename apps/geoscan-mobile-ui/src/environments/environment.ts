// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.
import { IEnvironment } from "@geoscan/main-lib/types";

export const environment: IEnvironment = {
    production: false,
    projectTitle: "GeoSCAN",
    logo: "geoscan-logo",
    logoSmall: "",
    logoFileExtension: ".svg",
    projectAuthCookieName: "auth_token",
    projectUserCookieName: "user",
    administrativeDiaryApiUrl: "${ADMINISTRATIVE_DIARY_API_URL}",
    cmsUrl: "${CMS_URL}",
    geoscanCmsUrl: "${GEOSCAN_CMS_URL}",
    geoscanApigsUrl: "${GEOSCAN_API_GSURL}",
    geoscanApiUrl: "${GEOSCAN_API_URL}",
    geoscanApiMobile: "${GEOSCAN_API_URL_MOBILE}",
    technofarmApiUrl: "${TECHNOFARM_API_URL}",
    technofarmLaravelApiUrl: "${TECHNOFARM_LARAVEL_API_URL}",
    azureMapsApiKey: "YOUR_AZURE_MAPS_API_KEY_HERE",
    googleMapsApiKey: "AIzaSyCP7hLKtuQLswTWA8THszoC16ph8so8jbw",
    bonusContractAreaPercent: 10,
    FarmTrackClientId: "GeoSCAN",
    FarmTrackLanguage: "en",
    mainNavigationInstance: "mobile",
    availableLanguages: [
        {
            value: "bg",
            label: "Bulgarian",
        },
        {
            value: "en",
            label: "English",
        },
        {
            value: "es",
            label: "Spanish",
        },
        {
            value: "ro",
            label: "Romanian",
        },
        {
            value: "ru",
            label: "Russian",
        },
        {
            value: "ua",
            label: "Ukrainian",
        },
        {
            value: "it",
            label: "Italian",
        },
    ],
    platformRequestInterval: 300000,
    machinesRequestInterval: 60000,
    pageSize: 10,
    reportPageSize: 20,
    layerAttributeInfoPageSize: 20,
    importLayerFilesHistoryPageSize: 20,
    splitLayoutWidth: 624,
    helpHeroId: "wGITfg4KDG",
    postHogApiKey: "",
    idpSettings: {
        authority: "http://keycloak.geoscan.info:8081/realms/geotech",
        client_id: "angular-idc",
        redirect_uri: "http://localhost:4200/signin-callback",
        scope: "openid profile",
        response_type: "code",
        post_logout_redirect_uri: "http://localhost:4200/signout-callback",
    },
    baseHref: "/",
    supportMail: "${GEOSCAN_SUPPORT_MAIL}",
    SENTRY_DSN: "${SENTRY_DSN_KEY}",
    SENTRY_TARGET_URLS: "${SENTRY_TARGET_URLS}",
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.

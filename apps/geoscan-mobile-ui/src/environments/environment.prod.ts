import { IEnvironment } from "@geoscan/main-lib/types";

export const environment: IEnvironment = {
    production: true,
    projectAuthCookieName: "auth_token",
    projectUserCookieName: "user",
    administrativeDiaryApiUrl: "${ADMINISTRATIVE_DIARY_API_URL}",
    cmsUrl: "https://cms-api.agrimi.com/api",
    geoscanCmsUrl: "https://api.agrimi.com/cms",
    geoscanApigsUrl: "https://api.agrimi.com/apigs",
    geoscanApiUrl: "https://api.agrimi.com",
    geoscanApiMobile: "https://api.agrimi.com/mobile",
    technofarmApiUrl: "https://app.agrimi.com/tf",
    azureMapsApiKey: "YOUR_AZURE_MAPS_API_KEY_HERE",
    googleMapsApiKey: "AIzaSyCP7hLKtuQLswTWA8THszoC16ph8so8jbw",
    bonusContractAreaPercent: 10,
    FarmTrackClientId: "GeoSCAN",
    FarmTrackLanguage: "en",
    mainNavigationInstance: "${MAIN_NAVIGATION_INSTANCE}",
    availableLanguages: [
        {
            value: "bg",
            label: "Bulgarian",
        },
        {
            value: "en",
            label: "English",
        },
        {
            value: "es",
            label: "Spanish",
        },
        {
            value: "ro",
            label: "Romanian",
        },
        {
            value: "ru",
            label: "Russian",
        },
        {
            value: "ua",
            label: "Ukrainian",
        },
        {
            value: "it",
            label: "Italian",
        },
    ],
    platformRequestInterval: 300000,
    machinesRequestInterval: 60000,
    pageSize: 10,
    reportPageSize: 20,
    layerAttributeInfoPageSize: 20,
    importLayerFilesHistoryPageSize: 20,
    splitLayoutWidth: 624,
    helpHeroId: "wGITfg4KDG",
    postHogApiKey: "${POST_HOG_API_KEY}",
    projectTitle: "",
    logo: "",
    logoSmall: "",
    logoFileExtension: "",
    idpSettings: {
        authority: "http://keycloak.agrimi.com:8081/realms/geotech",
        client_id: "angular-idc",
        redirect_uri: "http://localhost:4200/signin-callback",
        scope: "openid profile",
        response_type: "code",
        post_logout_redirect_uri: "http://localhost:4200/signout-callback",
    },
    baseHref: "${GS_BASE_HREF}",
    supportMail: "${GEOSCAN_SUPPORT_MAIL}",
    SENTRY_DSN:
        "https://<EMAIL>/4508460987056128",
    SENTRY_TARGET_URLS: [],
};

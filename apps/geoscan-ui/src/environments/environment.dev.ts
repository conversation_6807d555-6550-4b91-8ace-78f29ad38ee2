import { IEnvironment } from "@geoscan/main-lib/types";

export const environment: IEnvironment = {
    production: false,
    projectTitle: "Agrimi",
    logo: "agrimi-logo",
    logoSmall: "agrimi-logo-small",
    logoFileExtension: ".svg",
    projectAuthCookieName: "auth_token",
    projectUserCookieName: "user",
    administrativeDiaryApiUrl: "${ADMINISTRATIVE_DIARY_API_URL}",
    cmsUrl: "${CMS_URL}",
    geoscanCmsUrl: "${GEOSCAN_CMS_URL}",
    geoscanApigsUrl: "${GEOSCAN_API_GSURL}",
    geoscanApiUrl: "${GEOSCAN_API_URL}",
    technofarmApiUrl: "${TECHNOFARM_API_URL}",
    technofarmLaravelApiUrl: "${TECHNOFARM_LARAVEL_API_URL}",
    azureMapsApiKey: "YOUR_AZURE_MAPS_API_KEY_HERE",
    googleMapsApiKey: "AIzaSyCP7hLKtuQLswTWA8THszoC16ph8so8jbw",
    bonusContractAreaPercent: 10,
    FarmTrackClientId: "GeoSCAN",
    FarmTrackLanguage: "en",
    mainNavigationInstance: "web",
    availableLanguages: [
        {
            value: "bg",
            label: "Bulgarian",
        },
        {
            value: "en",
            label: "English",
        },
        {
            value: "es",
            label: "Spanish",
        },
        {
            value: "ro",
            label: "Romanian",
        },
        {
            value: "ru",
            label: "Russian",
        },
        {
            value: "ua",
            label: "Ukrainian",
        },
        {
            value: "it",
            label: "Italian",
        },
    ],
    platformRequestInterval: 60000,
    machinesRequestInterval: 60000,
    pageSize: 10,
    reportPageSize: 20,
    layerAttributeInfoPageSize: 20,
    importLayerFilesHistoryPageSize: 20,
    splitLayoutWidth: 624,
    splitLayoutWidthSmall: 524,
    resizableLayoutBufferWidth: 150,
    helpHeroId: "wGITfg4KDG",
    postHogApiKey: "",
    facebookPageURL: "https://www.facebook.com/geoscan.bg/",
    linkedinPageURL: "https://www.linkedin.com/company/4861481",
    idpSettings: {
        authority: "http://keycloak.geoscan.info:8081/realms/Staging",
        client_id: "geoscan-frontend",
        redirect_uri: "http://localhost:4200/signin-callback",
        scope: "openid profile",
        response_type: "code",
        post_logout_redirect_uri: "http://localhost:4205/signout-callback",
    },
    baseHref: "/",
    supportMail: "${GEOSCAN_SUPPORT_MAIL}",
    SENTRY_DSN: null,
    SENTRY_TARGET_URLS: null,
};

import { IEnvironment } from "@geoscan/main-lib/types";

export const environment: IEnvironment = {
    production: true,
    projectTitle: "AGViser",
    logo: "AGviser-logo",
    logoSmall: "AGviser-logo-small",
    logoFileExtension: ".svg",
    projectAuthCookieName: "auth_token",
    projectUserCookieName: "user",
    administrativeDiaryApiUrl: "${ADMINISTRATIVE_DIARY_API_URL}",
    cmsUrl: "${CMS_URL}",
    geoscanCmsUrl: "${GEOSCAN_CMS_URL}",
    geoscanApigsUrl: "${GEOSCAN_API_GSURL}",
    geoscanApiUrl: "${GEOSCAN_API_URL}",
    technofarmApiUrl: "${TECHNOFARM_API_URL}",
    technofarmLaravelApiUrl: "${TECHNOFARM_LARAVEL_API_URL}",
    azureMapsApiKey: "${AZURE_MAPS_API_KEY}",
    googleMapsApiKey: "${GOOGLE_MAPS_API_KEY}",
    bonusContractAreaPercent: 10,
    FarmTrackClientId: "GeoSCAN",
    FarmTrackLanguage: "en",
    mainNavigationInstance: "${MAIN_NAVIGATION_INSTANCE}",
    availableLanguages: [
        {
            value: "bg",
            label: "Bulgarian",
        },
        {
            value: "en",
            label: "English",
        },
        {
            value: "es",
            label: "Spanish",
        },
        {
            value: "ro",
            label: "Romanian",
        },
        {
            value: "ru",
            label: "Russian",
        },
        {
            value: "ua",
            label: "Ukrainian",
        },
        {
            value: "it",
            label: "Italian",
        },
    ],
    platformRequestInterval: 300000,
    machinesRequestInterval: 60000,
    pageSize: 10,
    reportPageSize: 20,
    layerAttributeInfoPageSize: 20,
    importLayerFilesHistoryPageSize: 20,
    splitLayoutWidth: 624,
    splitLayoutWidthSmall: 524,
    resizableLayoutBufferWidth: 150,
    helpHeroId: "${HELPHERO_ID}",
    postHogApiKey: "${POST_HOG_API_KEY}",
    idpSettings: {
        authority: "${KC_AUTHORITY}",
        client_id: "${KC_CLIENT_ID}",
        redirect_uri: "${KC_REDIRECT_URI}",
        scope: "${KC_SCOPE}",
        response_type: "${KC_RESPONSE_TYPE}",
        post_logout_redirect_uri: "${KC_POST_LOGOUT_REDIRECT_URI}",
    },
    baseHref: "${GS_BASE_HREF}",
    supportMail: "${GEOSCAN_SUPPORT_MAIL}",
    SENTRY_DSN: "",
    SENTRY_TARGET_URLS: undefined,
};

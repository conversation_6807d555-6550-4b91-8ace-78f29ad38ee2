export interface IEnvironment {
    production: boolean;
    projectAuthCookieName: string;
    projectUserCookieName: string;
    administrativeDiaryApiUrl?: string;
    cmsUrl?: string;
    geoscanCmsUrl?: string;
    geoscanApigsUrl?: string;
    geoscanApiUrl?: string;
    geoscanApiMobile?: string;
    technofarmApiUrl?: string;
    technofarmLaravelApiUrl?: string;
    azureMapsApiKey: string;
    googleMapsApiKey: string;
    bonusContractAreaPercent: number;
    FarmTrackClientId: string;
    FarmTrackLanguage: string;
    availableLanguages: { value: string; label: string }[];
    platformRequestInterval: number;
    machinesRequestInterval: number;
    pageSize: number;
    reportPageSize: number;
    layerAttributeInfoPageSize: number;
    importLayerFilesHistoryPageSize: number;
    splitLayoutWidth: number;
    splitLayoutWidthSmall?: number;
    resizableLayoutBufferWidth?: number;
    helpHeroId: string;
    postHogApiKey: string;
    projectTitle: string;
    logo: string;
    logoSmall: string;
    logoFileExtension: string;
    facebookPageURL?: string;
    linkedinPageURL?: string;
    idpSettings: object;
    baseHref: string;
    supportMail: string;
    mainNavigationInstance: string;
    SENTRY_DSN: string | null;
    SENTRY_TARGET_URLS: unknown;
}

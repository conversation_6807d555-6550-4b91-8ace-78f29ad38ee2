import {
    DestroyRef,
    ElementRef,
    Injectable,
    NgZone,
    Signal,
    inject,
    signal,
} from "@angular/core";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { AuthService } from "@geoscan/main-lib/auth";
import {
    AdministrativeMapService,
    GeometryOperationsService,
    HelperService,
} from "@geoscan/main-lib/services";
import {
    ElementEnum,
    IrrigationEventColorsEnum,
    IrrigationPlatformsColorByEvent,
    IrrigationPlatformsEventEnum,
    LabResultsRowType,
    LayerGroupsEnum,
    MachineStateEnum,
    MachineTrackType,
    MachineType,
    MachineTypeEnum,
    MachinesColorByStatus,
    MapLayerType,
    MapLayersEnum,
    MapLayersIdEnum,
    MapObjectDetailsType,
    MapOverlaysIdEnum,
    MapPopupEnum,
    MvtLayer,
    MvtLayerParamsType,
    PinType,
    SoilGridLayerClassGroupStyleEnum,
    SoilGridTrackLayersParamsType,
    TFMapLayerAttributesType,
    WMSLayerClientParamsType,
    WMSLayerDefaultParamsType,
    WMSLayerTechnofarmParamsType,
} from "@geoscan/main-lib/types";
import { Store } from "@ngrx/store";
import { TranslateService } from "@ngx-translate/core";
import { FeatureCollection, Feature as GeoJSONFeature } from "geojson";
import { cloneDeep, extend, isNil, min } from "lodash";
import moment from "moment";
import { Collection, Feature, ImageTile, MapBrowserEvent } from "ol";
import ProgressBar, {
    Options as ProgressBarOptions,
} from "ol-ext/control/ProgressBar";
import Tooltip from "ol-ext/overlay/Tooltip";
import FlowLine from "ol-ext/style/FlowLine";
import Map from "ol/Map";
import { unByKey } from "ol/Observable";
import Overlay from "ol/Overlay";
import TileState from "ol/TileState";
import View from "ol/View";
import { ColorLike } from "ol/colorlike";
import { Coordinate } from "ol/coordinate";
import { EventsKey } from "ol/events";
import { click } from "ol/events/condition";
import { Extent, buffer, getCenter } from "ol/extent";
import GeoJSON, { GeoJSONGeometry } from "ol/format/GeoJSON";
import { Geometry, MultiLineString, MultiPolygon, Polygon } from "ol/geom";
import Circle from "ol/geom/Circle";
import { Type as GeometryType } from "ol/geom/Geometry";
import LineString from "ol/geom/LineString";
import Point from "ol/geom/Point";
import {
    DragBox,
    DragPan,
    Draw,
    Interaction,
    Select,
    defaults as defaultInteractions,
} from "ol/interaction";
import { DragBoxEvent } from "ol/interaction/DragBox";
import { DrawEvent } from "ol/interaction/Draw";
import { SelectEvent } from "ol/interaction/Select";
import { VectorImage } from "ol/layer";
import BaseLayer from "ol/layer/Base";
import LayerGroup from "ol/layer/Group";
import TileLayer from "ol/layer/Tile";
import VectorLayer from "ol/layer/Vector";
import {
    ProjectionLike,
    getPointResolution,
    transform,
    transformExtent,
} from "ol/proj";
import { METERS_PER_UNIT } from "ol/proj/Units";
import { getRenderPixel } from "ol/render";
import RenderFeature, { toFeature } from "ol/render/Feature";
import Cluster from "ol/source/Cluster";
import TileSource from "ol/source/Tile";
import TileWMS from "ol/source/TileWMS";
import VectorSource from "ol/source/Vector";
import XYZ from "ol/source/XYZ";
import { Fill, Icon, RegularShape, Stroke, Style, Text } from "ol/style";
import CircleStyle from "ol/style/Circle";
import proj4 from "proj4";
import {
    Observable,
    Subject,
    combineLatest,
    filter,
    fromEventPattern,
    map,
    merge,
    startWith,
} from "rxjs";
import { MapSettingsSelectors } from "../public-api";
import { MapUiActions } from "./+state/actions";
import { MAP_CONFIG_TOKEN } from "./map-config.token";
@Injectable({
    providedIn: "root",
})
export class MapService {
    selectedFeatures$: Observable<Collection<Feature>>;
    showSwipe$: Observable<boolean>;

    platformsStyleCache: Record<string, Style> = {};

    // Define Top or Bottom position of the map click Popup
    get hasMapClickPopupTopPosition(): Signal<boolean> {
        return this.hasMapClickPopupTopPosition_.asReadonly();
    }
    setMapClickPopupTopPosition(hasTopPosition: boolean): void {
        this.hasMapClickPopupTopPosition_.set(hasTopPosition);
    }

    get bingLayer(): TileLayer<TileSource> {
        return this.bingLayer_;
    }

    get map(): Map {
        return this.map_;
    }

    get googleMap(): google.maps.Map {
        return this.googleMap_;
    }

    get markersLayer(): VectorLayer {
        return this.markersLayer_;
    }

    get weatherStationsLayer(): VectorLayer {
        return this.weatherStationsLayer_;
    }

    get platformsLayer(): LayerGroup {
        return this.platformsLayer_;
    }

    get platformsEmptyLayer(): VectorLayer {
        return this.platformsEmptyLayer_;
    }

    get pivotsActiveLayer(): VectorLayer {
        return this.pivotsActiveLayer_;
    }

    get pivotsIrrLayer(): VectorLayer {
        return this.pivotsIrrLayer_;
    }

    get pivotsOffLayer(): VectorLayer {
        return this.pivotsOffLayer_;
    }

    get pivotsPALayer(): VectorLayer {
        return this.pivotsPALayer_;
    }

    get pivotsMovementLayer(): VectorLayer {
        return this.pivotsMovementLayer_;
    }

    get pivotsWarningLayer(): VectorLayer {
        return this.pivotsWarningLayer_;
    }
    get pivotsCurrentPositionLayer(): VectorLayer {
        return this.pivotsCurrentPositionLayer_;
    }

    get irrigationTasksPositionLayer(): VectorLayer {
        return this.irrigationTasksPositionLayer_;
    }

    get irrigationTasksSegmentPositionLayer(): VectorLayer {
        return this.irrigationTasksSegmentPositionLayer_;
    }

    get irrigationTasksTransportationLayer(): VectorLayer {
        return this.irrigationTasksTransportationLayer_;
    }

    get machinesLayer(): LayerGroup {
        return this.machinesLayer_;
    }

    get machinesOffLayer(): VectorLayer {
        return this.machinesOffLayer_;
    }

    get machinesIdleLayer(): VectorLayer {
        return this.machinesIdleLayer_;
    }

    get machinesMovingLayer(): VectorLayer {
        return this.machinesMovingLayer_;
    }

    get machinesOfflineLayer(): VectorLayer {
        return this.machinesOfflineLayer_;
    }

    get machinesTrackLayer(): VectorImage {
        return this.machinesTrackLayer_;
    }

    get machinesEventsTrackLayer(): VectorLayer {
        return this.machinesEventsTrackLayer_;
    }

    get machinesEventsWorkAreaLayer(): VectorLayer {
        return this.machinesEventsWorkAreaLayer_;
    }

    get soilVraVectorLayer(): VectorLayer {
        return this.soilVraVectorLayer_;
    }

    get leftThematicLayer(): TileLayer<TileWMS> {
        return this.leftThematicLayer_;
    }

    get rightThematicLayer(): TileLayer<TileWMS> {
        return this.rightThematicLayer_;
    }

    get centreThematicLayer(): TileLayer<TileWMS> {
        return this.centreThematicLayer_;
    }

    get rgbByDateLayer(): TileLayer<TileSource> {
        return this.rgbByDateLayer_;
    }

    get soilVraWmsLayer(): TileLayer<TileWMS> {
        return this.soilVraWmsLayer_;
    }

    get measurementLayer(): VectorLayer {
        return this.measurementLayer_;
    }

    get plotsByCropLayer(): TileLayer<TileWMS> {
        return this.plotsByCropLayer_;
    }

    get externalTechnofarmMapcacheLayer(): TileLayer<TileWMS> {
        return this.externalTechnofarmMapcacheLayer_;
    }

    get geolocationLayer(): VectorLayer<VectorSource<Feature<Point>>> {
        return this.geolocationLayer_;
    }

    get soilGridLayer() {
        return this.soilGridLayer_;
    }

    get soilTrackLayer() {
        return this.soilTrackLayer_;
    }

    get selectedPlotLayer(): VectorLayer {
        return this.selectedPlotLayer_;
    }

    get technofarmLayer(): MvtLayer {
        return this.technofarmLayer_;
    }

    get technofarmMapcacheLayer(): MvtLayer {
        return this.technofarmMapcacheLayer_;
    }

    get surveysLayer(): VectorLayer {
        return this.surveysLayer_;
    }

    get kvsManageConractsLayer(): VectorLayer {
        return this.kvsManageConractsLayer_;
    }

    get saveMeasurementOverlay(): Overlay {
        return this.map.getOverlayById(
            MapOverlaysIdEnum.SaveMeasurementOverlayId
        );
    }

    get fieldsBoundariesLayer(): MvtLayer {
        return this.fieldsBoundariesLayer_;
    }

    get dragBoxRightActive(): boolean {
        return this.dragBoxRightInteraction.getActive();
    }

    defaultExtent: Extent;
    wmsServer: string;
    serverMapPath: string;
    mapcacheUrl: string;
    epsgProj: string;

    technofarmSelectedFeatureIds: string[] = [];
    fieldBoundariesSelectedFeatureGid: string | number;

    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private map_: Map;
    private googleMap_: google.maps.Map;
    private showSwipeSource = new Subject<boolean>();
    private selectedFeatures = new Subject<Collection<Feature>>();
    private measurementToolTip: any;

    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private markersLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private weatherStationsLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private platformsLayer_: LayerGroup;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private pivotsActiveLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private pivotsIrrLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private pivotsOffLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private pivotsPALayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private pivotsMovementLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private pivotsWarningLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private platformsEmptyLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private pivotsCurrentPositionLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private irrigationTasksLayer_: LayerGroup;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private irrigationTasksPositionLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private irrigationTasksSegmentPositionLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private irrigationTasksTransportationLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private machinesLayer_: LayerGroup;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private machinesOffLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private machinesIdleLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private machinesMovingLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private machinesOfflineLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private machinesEventsTrackLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private machinesTrackLayer_: VectorImage;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private machinesEventsWorkAreaLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private soilVraVectorLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private rgbByDateLayer_: TileLayer<TileSource>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private soilSamplesLayer_: VectorLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private soilGridLayer_: TileLayer<TileWMS>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private soilTrackLayer_: TileLayer<TileWMS>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private soilByElementPhLayer_: TileLayer<TileWMS>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private soilByElementP2O5Layer_: TileLayer<TileWMS>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private soilByElementTMNLayer_: TileLayer<TileWMS>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private soilByElementK2OLayer_: TileLayer<TileWMS>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private leftThematicLayer_: TileLayer<TileWMS>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private rightThematicLayer_: TileLayer<TileWMS>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private centreThematicLayer_: TileLayer<TileWMS>;
    private fieldsBoundariesLayer_: MvtLayer;
    // eslint-disable-next-line @typescript-eslint/naming-convention, no-underscore-dangle, id-blacklist, id-match
    private soilVraWmsLayer_: TileLayer<TileWMS>;
    private bingLayer_: TileLayer<TileSource>;
    // eslint-disable-next-line @typescript-eslint/naming-convention,no-underscore-dangle,id-blacklist,id-match
    private measurementLayer_: VectorLayer;
    private plotsByCropLayer_: TileLayer<TileWMS>;
    private geolocationLayer_: VectorLayer<VectorSource<Feature<Point>>>;
    private selectedPlotLayer_: VectorLayer;
    private externalTechnofarmMapcacheLayer_: TileLayer<TileWMS>;
    private technofarmLayer_: MvtLayer;
    private technofarmMapcacheLayer_: MvtLayer;
    private surveysLayer_: VectorLayer;
    private kvsManageConractsLayer_: VectorLayer;

    private wmsLayerDefaultParams: WMSLayerDefaultParamsType;

    private dragPanInteraction: DragPan;
    private selectInteraction: Select;
    private dragBoxInteraction: DragBox;
    private dragBoxRightInteraction: DragBox;

    private measurementCoordinates: Coordinate;

    private mapHoverEventKey: EventsKey;
    private mapPopupClickEventKey: EventsKey;
    private mapCreateMarkerEventKey: EventsKey;
    private mapSelectInteractionClickEventKey: EventsKey;
    private mapDrawChangeEventKey: EventsKey;
    private mapViewChangeEventKey: EventsKey[];

    private technofarmHoveredFeaturesIds: string[] = [];

    private enableMapHover: boolean;

    private hasMapClickPopupTopPosition_ = signal<boolean>(false);

    private authService = inject(AuthService);
    private store = inject(Store);
    private zone = inject(NgZone);
    private translate = inject(TranslateService);
    private helperService = inject(HelperService);
    private config = inject(MAP_CONFIG_TOKEN);
    private geometryOperationsService = inject(GeometryOperationsService);
    private administrativeMapService = inject(AdministrativeMapService);
    private destroyRef = inject(DestroyRef);

    constructor() {
        this.showSwipe$ = this.showSwipeSource.asObservable();
        this.selectedFeatures$ = this.selectedFeatures.asObservable();
    }

    initMap(
        target: ElementRef,
        defaultExtent: Extent,
        wmsServer: string,
        serverMapPath: string,
        mapcacheUrl: string,
        epsgProj: string
    ): void {
        this.defaultExtent = defaultExtent;
        this.wmsServer = wmsServer;
        this.serverMapPath = serverMapPath;
        this.mapcacheUrl = mapcacheUrl;
        this.epsgProj = epsgProj;

        this.wmsLayerDefaultParams = {
            SERVICE: "WMS",
            VERSION: "1.1.1",
            REQUEST: "GetMap",
            FORMAT: "image/png",
            TRANSPARENT: true,
            MAP: this.serverMapPath + "orders_work" + ".map",
        };

        this.bingLayer_ = new TileLayer({
            visible: true,
            preload: Infinity,
            source: new BingMaps({
                key: this.config.bingMapsApiKey,
                imagerySet: "AerialWithLabels",
                placeholderTiles: true,
            }),
        });
        this.bingLayer_.setProperties({
            name: "bingLayer",
            id: MapLayersIdEnum.BingLayerId,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });

        this.map_ = new Map({
            layers: [],
            target: target.nativeElement,
            pixelRatio: min([devicePixelRatio, 2]),
            controls: [],
            view: new View({
                center: getCenter(this.defaultExtent),
                zoom: 7.9,
                maxZoom: 28,
                projection: "EPSG:3857",
            }),
            interactions: defaultInteractions({
                doubleClickZoom: false,
                dragPan: true,
                keyboard: false,
                pinchRotate: false,
            }),
        });

        setTimeout(() => {
            this.store.dispatch(MapUiActions.initMap());
        }, 0);

        this.initProjection();
        this.initLayers();
        this.addMapHoverListener();
        this.onMapClickAddPopup();
        this.initMapProgressBarControl();
        this.addZoomLevelChangeListener();
        this.getMapSettings();
    }

    initGoogleMap(map: google.maps.Map) {
        this.googleMap_ = map;

        if (!this.map) {
            return;
        }

        const olMapView = this.map.getView();

        this.googleMap_.setOptions({
            minZoom: olMapView.getMinZoom(),
            maxZoom: olMapView.getMaxZoom(),
        });

        /* Synchronize google map with openlayers map */
        this.mapViewChangeEventKey = olMapView.on(
            ["change:center", "change:resolution"],
            () => {
                const [lng, lat] = olMapView.getCenter();
                const [lngTransformed, latTransformed] =
                    this.transformCoordinate(
                        olMapView.getProjection().getCode(),
                        "EPSG:4326",
                        [lng, lat]
                    );

                this.googleMap_.moveCamera({
                    center: new google.maps.LatLng({
                        lng: lngTransformed,
                        lat: latTransformed,
                    }),
                    zoom: olMapView.getZoom(),
                });
            }
        );

        olMapView.dispatchEvent("change:center");
    }

    stopGoogleMapSync() {
        unByKey(this.mapViewChangeEventKey);
    }

    initMapProgressBarControl() {
        const tileLayers = this.map_
            .getLayers()
            .getArray()
            .filter((layer) => layer instanceof TileLayer);
        const progressBar = new ProgressBar({
            layers: tileLayers,
        } as ProgressBarOptions);

        this.map_.addControl(progressBar);
    }

    addZoomLevelChangeListener() {
        let visibleMap: boolean;

        this.map.getView().on("change:resolution", () => {
            const zoom = this.map.getView().getZoom();

            if (zoom > 19 && (isNil(visibleMap) || visibleMap)) {
                this.store.dispatch(
                    MapUiActions.toggleBaseLayerVisibilityOnZoomChange({
                        exceedsZoomLevel: true,
                    })
                );

                visibleMap = false;
            }

            if (zoom <= 19 && (isNil(visibleMap) || !visibleMap)) {
                this.store.dispatch(
                    MapUiActions.toggleBaseLayerVisibilityOnZoomChange({
                        exceedsZoomLevel: false,
                    })
                );

                visibleMap = true;
            }
        });
    }

    getMapSettings() {
        this.store
            .select(MapSettingsSelectors.selectEnableMapHover)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(
                (enableMapHover: boolean) =>
                    (this.enableMapHover = enableMapHover)
            );
    }

    initProjection() {
        const epsgProj = this.authService.epsgProj();
        const proj4js = this.authService.proj4jsString();

        if (epsgProj && proj4js) {
            proj4.defs(`EPSG:${epsgProj}`, proj4js);
        }
    }

    setMapLoading(loading: boolean): void {
        this.map.getTargetElement().style.cursor = loading ? "progress" : "";
    }

    addLayer(layer: BaseLayer): void {
        this.map_.addLayer(layer);
    }

    removeLayer(layer: BaseLayer): void {
        this.map_.removeLayer(layer);
    }

    showHideAllLayers(showAllLayers: boolean) {
        this.map
            .getLayers()
            .getArray()
            .forEach((layer: BaseLayer) => {
                if (layer.get("disable") === false) {
                    layer.setVisible(showAllLayers);
                }
                return layer;
            });
    }

    zoomToExtent(
        extent: Extent = this.defaultExtent,
        padding: Array<number> = [30, 30, 30, 30]
    ): void {
        this.map_.getView().fit(extent, {
            padding,
            duration: 800,
            maxZoom: 19,
        });
    }

    getExtentFromGeoJSONGeometry(geometry: GeoJSONGeometry): Extent;
    getExtentFromGeoJSONGeometry(feature: GeoJSONFeature): Extent;
    getExtentFromGeoJSONGeometry(
        geomJSON: GeoJSONGeometry | GeoJSONFeature,
        sourceProjection: ProjectionLike,
        destinationProjection: ProjectionLike
    ): Extent;
    getExtentFromGeoJSONGeometry(
        geomJSON: GeoJSONGeometry | GeoJSONFeature,
        sourceProjection?: ProjectionLike,
        destinationProjection?: ProjectionLike
    ): Extent {
        const olFeature = new GeoJSON().readFeature(geomJSON) as Feature;
        let extent = olFeature.getGeometry().getExtent();

        if (sourceProjection && destinationProjection) {
            extent = transformExtent(
                extent,
                sourceProjection,
                destinationProjection
            );
        }

        return extent;
    }

    zoomToPoint(longitude: number, latitude: number, zoomLevel = 15) {
        this.map_.getView().animate({
            center: proj4("EPSG:4326", "EPSG:3857", [longitude, latitude]),
            zoom: zoomLevel,
        });
    }

    addInteraction(interaction: Interaction): void {
        this.map_.addInteraction(interaction);
    }

    addMapDragPanInteraction(): void {
        this.dragPanInteraction = new DragPan();
        this.addInteraction(this.dragPanInteraction);
    }

    dragPanSetActive(active: boolean): void {
        this.dragPanInteraction.setActive(active);
    }

    disableMeasurementTool(): void {
        const measureDetailsPopup = this.map.getOverlayById(
            MapLayersIdEnum.MeasurementLayerId
        );
        measureDetailsPopup
            .getElement()
            .parentElement.classList.remove("visible");

        const measurementLayer = this.getLayerByName(
            MapLayersIdEnum.MeasurementLayerId
        ) as VectorLayer;
        measurementLayer.getSource().clear();

        this.saveMeasurementOverlay &&
            this.map.removeOverlay(this.saveMeasurementOverlay);
    }

    measurementInteractionSetActive(
        active: boolean,
        type?: GeometryType,
        canSaveGeometry: boolean = false
    ): void {
        const maximumFractionDigits = 3;
        const geometryTypes = [
            "Point",
            "LineString",
            "LinearRing",
            "Polygon",
            "MultiPoint",
            "MultiLineString",
            "MultiPolygon",
            "GeometryCollection",
            "Circle",
        ];
        this.map_
            .getInteractions()
            .getArray()
            .filter((interaction: Interaction) =>
                Object.values(geometryTypes).includes(interaction.get("type"))
            )
            .forEach((interaction) => {
                interaction.setActive(false);
            });

        if (this.measurementToolTip instanceof Tooltip) {
            this.measurementToolTip.hide();
            this.measurementToolTip.removeFeature();
        }

        const measurementInteraction = this.map_
            .getInteractions()
            .getArray()
            .find(
                (interaction: Interaction) => interaction.get("type") === type
            ) as Draw;

        measurementInteraction.setActive(active);
        this.measurementLayerVisibiliy(active);

        if (active === false) {
            return this.disableMeasurementTool();
        }

        this.measurementToolTip = new Tooltip({
            Positioning: "center-center",
            maximumFractionDigits: maximumFractionDigits,
        });

        this.measurementToolTip.formatLength = (length: number) => {
            return this.formatMeasurementLength(length);
        };

        this.measurementToolTip.formatArea = (area: number) => {
            const areaUnitLabel = this.authService.getAreaUnitLabel();
            const formattedArea = `${this.formatMeasurementArea(
                area
            )} ${areaUnitLabel}`;

            return formattedArea;
        };

        this.map_.addOverlay(this.measurementToolTip);

        if (canSaveGeometry && type === "Polygon") {
            this.createSaveMeasurementOverlay();
        }

        measurementInteraction.on("drawstart", (e) => {
            this.measurementStart(e);

            if (canSaveGeometry) {
                this.saveMeasurementOverlay &&
                    this.saveMeasurementOverlay
                        .getElement()
                        .parentElement.classList.remove("visible");
            }
        });
        measurementInteraction.on("drawend", (e) => {
            this.measurementEnd(e);
            unByKey(this.mapDrawChangeEventKey);

            if (canSaveGeometry && type === "Polygon") {
                this.showSaveMeasurementOverlay(e.feature);
            }
        });
    }

    formatMeasurementLength(length: number) {
        return (
            Math.floor(length).toLocaleString(undefined, {
                maximumFractionDigits: 0,
            }) +
            " " +
            this.translate.instant("m")
        );
    }

    formatMeasurementArea(area: number) {
        const areaCoef = this.authService.getAreaCoef();
        const maximumFractionDigits = 3;

        let calculatedArea = 0;
        if (area > Math.pow(10, -1 * maximumFractionDigits)) {
            calculatedArea = (area / 1000) * areaCoef;
        }

        const formattedArea = calculatedArea.toLocaleString(undefined, {
            maximumFractionDigits: maximumFractionDigits,
        });

        return formattedArea;
    }

    createMeasureOverlay(popupElement: HTMLElement): void {
        const measureTooltip = new Overlay({
            element: popupElement,
            id: MapLayersIdEnum.MeasurementLayerId,
            stopEvent: false,
            insertFirst: false,
        });
        this.map_.addOverlay(measureTooltip);
    }

    createSaveMeasurementOverlay() {
        const saveIconElement = document.createElement("div");
        saveIconElement.innerHTML = `<span class="gs-icon-save"></span>`;
        const spanElement = saveIconElement.querySelector("span");
        saveIconElement.style.cssText = `
                width: 30px;
                height: 30px;
                background-color: #333333;
                border-radius: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

        spanElement.style.cssText = `
                width: 18px;
                height: 18px;
                background-color: white;
            `;

        const saveMeasurementOverlay = new Overlay({
            element: saveIconElement,
            id: MapOverlaysIdEnum.SaveMeasurementOverlayId,
        });
        this.map_.addOverlay(saveMeasurementOverlay);
    }

    showSaveMeasurementOverlay(feature: Feature<Geometry>) {
        const polygon = feature.getGeometry();
        const extent = polygon.getExtent();
        const center = getCenter(extent);
        const tooltipPosition = this.measurementToolTip.getPosition();
        const saveIconElement = this.saveMeasurementOverlay.getElement();
        this.saveMeasurementOverlay
            .getElement()
            .parentElement.classList.add("visible");
        this.saveMeasurementOverlay.setPositioning(
            tooltipPosition ? "bottom-center" : "center-center"
        );

        if (tooltipPosition) {
            const iconPosition = [tooltipPosition[0], tooltipPosition[1]];
            this.saveMeasurementOverlay.setPosition(iconPosition);
        } else {
            this.saveMeasurementOverlay.setPosition(center);
        }

        if (!saveIconElement.getAttribute("data-click-listener")) {
            saveIconElement.addEventListener("click", () => {
                const newFeature = this.measurementLayer_
                    .getSource()
                    .getFeatures()[0];
                const isValidGeometry =
                    this.geometryOperationsService.validateGeometry([
                        newFeature,
                    ] as Feature<Polygon | MultiPolygon>[]);

                isValidGeometry
                    ? this.store.dispatch(
                          MapUiActions.openSaveMeasurementDrawer({
                              feature: cloneDeep(newFeature),
                          })
                      )
                    : this.store.dispatch(
                          MapUiActions.invalidSaveMeasurementGeometry()
                      );
            });
        }

        saveIconElement.setAttribute("data-click-listener", "true");
    }

    getLayerByName(name: string): BaseLayer {
        return this.map
            .getLayers()
            .getArray()
            .find((layer: BaseLayer) => layer.get("name") === name);
    }

    swipe(layer: TileLayer<TileSource>, swipeWidth: number) {
        layer.on("prerender", (event) => {
            const ctx = event.context as CanvasRenderingContext2D;
            const mapSize = this.map.getSize();
            const width = mapSize[0] * swipeWidth;
            const tl = getRenderPixel(event, [width, 0]);
            const tr = getRenderPixel(event, [mapSize[0], 0]);
            const bl = getRenderPixel(event, [width, mapSize[1]]);
            const br = getRenderPixel(event, mapSize);

            ctx.save();
            ctx.beginPath();
            ctx.moveTo(tl[0], tl[1]);
            ctx.lineTo(bl[0], bl[1]);
            ctx.lineTo(br[0], br[1]);
            ctx.lineTo(tr[0], tr[1]);
            ctx.closePath();
            ctx.clip();
        });

        layer.on("postrender", (event) => {
            const ctx = event.context as CanvasRenderingContext2D;
            ctx.restore();
        });
    }

    initLayers() {
        this.initPlotsByCropLayer();
        this.initSoilByElementPHLayer(); // TODO:: GPS-1776
        this.initSoilByElementP2O5Layer(); // TODO:: GPS-1776
        this.initSoilByElementTMNLayer(); // TODO:: GPS-1776
        this.initSoilByElementK2OLayer(); // TODO:: GPS-1776
        this.initRgbByDateLayer();
        this.initLeftThematicLayer();
        this.initRightThematicLayer();
        this.initCentreThematicLayer();
        this.initExternalTechnofarmMapcacheLayer();
        this.initFieldsBoundariesLayer();
        this.initSoilGridLayer();
        this.initSoilTrackLayer();
        this.initTechnofarmLayer();
        this.initTechnofarmMapcacheLayer();
        this.initSurveysLayer();
        this.initKvsManageConractsLayer();
        this.initSoilSamplesLayer();
        this.initSelectedPlotLayer();
        this.initWeatherStationsLayer();
        this.initIrrigationTasksPositionLayer();
        this.irrigationTasksSegmentsLayer();
        this.initIrrigationTasksTransportationLayer();
        this.initIrrigationTasksLayer();
        this.initPivotsCurrentPositionLayer();
        this.initPlatformsEmptyLayer();
        this.initPivotsActiveLayer();
        this.initPivotsIrrLayer();
        this.initPivotsOffLayer();
        this.initPivotsPALayer();
        this.initPivotsMovementLayer();
        this.initPivotsWarningLayer();
        this.initPlatformsLayer();
        this.initMarkersLayer();
        this.initMachinesEventsWorkAreaLayer();
        this.initMachinesOffLayer();
        this.initMachinesIdleLayer();
        this.initMachinesMovingLayer();
        this.initMachinesOfflineLayer();
        this.initMachinesEventsTrackLayer();
        this.initMachinesTrackLayer();
        this.initMachinesLayer();
        this.initSoilVraWmsLayer();
        this.initSoilVraVectorLayer();
        this.initMeasurementLayer();
        this.initGeolocationLayer();
    }

    callSoilSamplesLayer(samples: LabResultsRowType[]) {
        if (!samples || samples?.length === 0) {
            return;
        }
        const features: Feature[] = [];
        samples.forEach((result: LabResultsRowType) => {
            const point = new GeoJSON().readGeometry(result.geom_json);
            point.transform("EPSG:4326", "EPSG:3857");
            const feature = new Feature(point);
            feature.setId(result.cell_id);
            features.push(feature);
        });

        const vectorSource = new VectorSource({
            features,
        });

        this.soilSamplesLayer_.setSource(vectorSource);
        this.soilSamplesLayer_.setVisible(true);
        this.soilSamplesLayer_.set("selectable", true);
        this.zoomToExtent(vectorSource.getExtent());
    }

    initDetailsPopupOverlay(popupElement: HTMLElement) {
        /**
         * Needed because of openlayers stopEvent not working as intended.
         * Fix "pointermove" events dispatched even when hover over the map popup
         * check https://github.com/openlayers/openlayers/issues/4953
         */
        const popupOverlay = popupElement.parentElement.querySelector(
            ".ol-overlaycontainer-stopevent"
        );

        const addPointermoveHandler = (handler: (event: Event) => void) =>
            popupOverlay.addEventListener("pointermove", handler);

        const removePointermoveHandler = (handler: (event: Event) => void) =>
            popupOverlay.removeEventListener("pointermove", handler);

        fromEventPattern(addPointermoveHandler, removePointermoveHandler)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((event: Event) => {
                event.stopPropagation();
            });

        const addPointerenterHandler = (handler: (event: Event) => void) =>
            popupOverlay.addEventListener("pointerenter", handler);

        const removePointerenterHandler = (handler: (event: Event) => void) =>
            popupOverlay.removeEventListener("pointerenter", handler);

        fromEventPattern(addPointerenterHandler, removePointerenterHandler)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((event: Event) => {
                // reset cursor when hover over the popup
                this.map.getTargetElement().style.cursor = "";
            });

        const overlay = new Overlay({
            id: MapOverlaysIdEnum.PopupOverlayId,
            element: popupElement,
            autoPan: {
                animation: {
                    duration: 250,
                },
                margin: 70,
            },
        });
        this.map.addOverlay(overlay);
    }

    removeDetailsPopupOverlay() {
        this.map.getOverlayById(MapOverlaysIdEnum.PopupOverlayId);
    }

    addMapHoverListener() {
        const addPointerDownHandler = (handler: (event: MouseEvent) => void) =>
            this.map
                .getTargetElement()
                .addEventListener("pointerdown", handler);
        const removePointerDownHandler = (
            handler: (event: MouseEvent) => void
        ) =>
            this.map
                .getTargetElement()
                .removeEventListener("pointerdown", handler);

        const addPointerUpHandler = (handler: (event: MouseEvent) => void) =>
            this.map.getTargetElement().addEventListener("pointerup", handler);
        const removePointerUpHandler = (handler: (event: MouseEvent) => void) =>
            this.map
                .getTargetElement()
                .removeEventListener("pointerup", handler);

        const addPointerMoveHandler = (
            handler: (event: MapBrowserEvent<PointerEvent>) => void
        ) => this.map.on("pointermove", handler);

        const removePointerMoveHandler = (
            handler: (event: MapBrowserEvent<PointerEvent>) => void
        ) => this.map.on("pointermove", handler);

        const pointerMove$ = fromEventPattern<MapBrowserEvent<PointerEvent>>(
            addPointerMoveHandler,
            removePointerMoveHandler
        );

        const pointerUp$ = merge(
            fromEventPattern<PointerEvent>(
                addPointerUpHandler,
                removePointerUpHandler
            ),
            fromEventPattern<PointerEvent>(
                addPointerDownHandler,
                removePointerDownHandler
            )
        ).pipe(
            map((event) => event.type === "pointerup"),
            startWith(false)
        );

        combineLatest([pointerMove$, pointerUp$])
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                filter(([_pointerMove, pointerUp]) => pointerUp),
                map(([pointerMove, _pointerUp]) => pointerMove)
            )
            .subscribe((event) => {
                //if the select from map is enabled don't set the default cursor pointer
                if (!this.administrativeMapService.isSelectFromMapEnabled()) {
                    this.setCursorPointer(event);
                } else {
                    const mapTargetElement = this.map.getTargetElement();
                    if (mapTargetElement.style.cursor === "pointer") {
                        mapTargetElement.style.cursor = "";
                    }
                }
                this.enableMapHover && this.highlightTechnofarmFeature(event);
            });
    }

    onMapClickAddPopup() {
        if (this.mapPopupClickEventKey) {
            this.stopMapPopupClickListener();
        }

        this.mapPopupClickEventKey = this.map.on("click", (event) => {
            //close the popup before asigning new position
            this.closeDetailsPopup();
            // Get the height of the viewport and click position to
            // define the Top or Bottom position of the popup
            const viewportHeight = window.innerHeight;
            const cllientY = Math.round(event.originalEvent.clientY);
            const spaceAbove = Math.round(cllientY);
            const spaceBelow = Math.round(viewportHeight - cllientY);
            const hasTopPosition = spaceAbove > spaceBelow;
            this.setMapClickPopupTopPosition(hasTopPosition);

            const mapObjectDetails = {} as MapObjectDetailsType;
            const selectedFeatures: Array<Feature | RenderFeature> = [];
            const stopAtFirstFeatureLayers = [
                MapPopupEnum.Machines,
                MapPopupEnum.Platforms,
                MapLayersIdEnum.MarkersLayerId,
                MapLayersIdEnum.WeatherStationsLayerId,
            ];
            const popupLayers = [
                MapPopupEnum.Machines,
                MapPopupEnum.Platforms,
                MapLayersIdEnum.MarkersLayerId,
                MapLayersIdEnum.WeatherStationsLayerId,
                MapLayersIdEnum.FieldsBoundariesLayerId,
                MapLayersIdEnum.TechnofarmLayerId,
                MapLayersIdEnum.TechnofarmMapcacheLayerId,
            ];

            this.map.forEachFeatureAtPixel(
                event.pixel,
                (feature, layer) => {
                    const layerGroup = layer.getProperties().groupName;

                    if (
                        layerGroup &&
                        stopAtFirstFeatureLayers.includes(layerGroup)
                    ) {
                        mapObjectDetails.type = layerGroup;

                        // For these layers, stop searching after the first feature
                        selectedFeatures.push(feature);
                        return true; // Stops the iteration
                    }

                    const layerName = layer.getProperties().name;
                    const layerId = layer.getProperties().id;
                    mapObjectDetails.type = [
                        MapLayersIdEnum.TechnofarmLayerId,
                        MapLayersIdEnum.TechnofarmMapcacheLayerId,
                    ].includes(layerId)
                        ? MapPopupEnum.Fields
                        : layerName;

                    if (stopAtFirstFeatureLayers.includes(layerId)) {
                        selectedFeatures.push(feature);
                        return true;
                    }

                    // Collect all features for the other layers
                    selectedFeatures.push(feature);
                    // Return false to continue searching
                    return false;
                },
                {
                    layerFilter: (layer) => {
                        const layerGroup = layer.getProperties().groupName;
                        const layerId = layer.getProperties().id;

                        return (
                            (layerGroup && popupLayers.includes(layerGroup)) ||
                            popupLayers.includes(layerId)
                        );
                    },
                }
            );

            if (!selectedFeatures.length) {
                this.store.dispatch(
                    MapUiActions.getFieldForPopup({
                        coordinate: event.coordinate,
                    })
                );
                return;
            }

            if (
                selectedFeatures.length === 1 &&
                mapObjectDetails.type !== MapPopupEnum.Fields
            ) {
                const selectedFeature = selectedFeatures[0];
                const featureProps = selectedFeature.getProperties();

                if (
                    Object.prototype.hasOwnProperty.call(
                        featureProps,
                        "features"
                    )
                ) {
                    mapObjectDetails.data = cloneDeep(featureProps.features);
                    mapObjectDetails.coordinate = (
                        featureProps.geometry as Point
                    ).getCoordinates();
                } else {
                    mapObjectDetails.coordinate = (
                        selectedFeature.getGeometry() as Point
                    ).getCoordinates();

                    switch (mapObjectDetails.type) {
                        case MapPopupEnum.Machines: {
                            mapObjectDetails.data = (
                                featureProps as MachineType
                            ).wialon_unit_imei;
                            break;
                        }
                        case MapPopupEnum.WeatherStations: {
                            const geojsonFormatter = new GeoJSON();
                            const geoJsonFeature =
                                geojsonFormatter.writeFeatureObject(
                                    selectedFeature as Feature
                                );

                            mapObjectDetails.data = geoJsonFeature;
                            break;
                        }
                        case MapPopupEnum.Markers: {
                            mapObjectDetails.data =
                                featureProps as Partial<PinType>;
                            break;
                        }
                    }
                }
            }

            if (mapObjectDetails.type === MapPopupEnum.Fields) {
                const geoJSONFeatures = selectedFeatures.map(
                    (renderFeature: RenderFeature) => {
                        const feature = toFeature(renderFeature);
                        const geoJSONFeature = new GeoJSON().writeFeatureObject(
                            feature
                        );

                        return geoJSONFeature;
                    }
                );

                mapObjectDetails.data = geoJSONFeatures;
                mapObjectDetails.coordinate = event.coordinate;
            }

            this.store.dispatch(
                MapUiActions.selectMapObject({
                    mapObjectDetails,
                })
            );
        });
    }

    convertFeaturesToGeoJSON(features: Feature | Feature[]): FeatureCollection {
        return new GeoJSON().writeFeaturesObject(
            Array.isArray(features) ? features : [features]
        );
    }

    normalizeToGeoJSONFeatureCollection(
        feature: RenderFeature | Feature | RenderFeature[] | Feature[]
    ): FeatureCollection {
        if (feature) {
            const normalizeFeatureFn = (f: RenderFeature | Feature): Feature =>
                f instanceof RenderFeature ? toFeature(f) : f;

            const normalizedFeature = Array.isArray(feature)
                ? feature.map(normalizeFeatureFn)
                : normalizeFeatureFn(feature);

            return this.convertFeaturesToGeoJSON(normalizedFeature);
        } else {
            return;
        }
    }

    getFeatureAtPixel(event: MapBrowserEvent<MouseEvent>) {
        const selectedFeature = this.map.forEachFeatureAtPixel(
            event.pixel,
            (feature) => feature
        );

        if (selectedFeature) {
            return selectedFeature;
        }
    }

    showDetailsPopup(coordinate: Coordinate) {
        const detailsPopup = this.map.getOverlayById(
            MapOverlaysIdEnum.PopupOverlayId
        );

        this.closeDetailsPopup(); // we call this method for animation purposes
        setTimeout(() => {
            // Show popup on timeout (for animation purposes)
            detailsPopup.setPosition(coordinate);
            this.map.getTargetElement().style.cursor = "";
            this.technofarmHoveredFeaturesIds = [];
        }, 0.5);
    }

    closeDetailsPopup() {
        const detailsPopup = this.map.getOverlayById(
            MapOverlaysIdEnum.PopupOverlayId
        );

        if (detailsPopup) {
            detailsPopup.setPosition(undefined);
        }
    }

    stopMapHoverListener() {
        unByKey(this.mapHoverEventKey);
    }

    stopMapPopupClickListener() {
        unByKey(this.mapPopupClickEventKey);
    }

    addMapDragBoxInteraction(): void {
        this.dragBoxInteraction = new DragBox();
        this.addInteraction(this.dragBoxInteraction);
        this.dragBoxSetActive(false);
    }

    addMapDragBoxRightInteraction() {
        this.dragBoxRightInteraction = new DragBox({
            condition: (event) => {
                const mouseEvent = event.originalEvent as MouseEvent;
                return mouseEvent.button === 2;
            },
        });
        this.addInteraction(this.dragBoxRightInteraction);
        this.dragBoxRightSetActive(false);
    }

    dragBoxSetActive(active: boolean): void {
        this.dragBoxInteraction.setActive(active);
    }

    //Activate dragbox on drag with right mouse key
    dragBoxRightSetActive(active: boolean): void {
        this.dragBoxRightInteraction.setActive(active);
        if (this.dragBoxRightInteraction.getActive()) {
            this.map.getViewport().addEventListener("contextmenu", (event) => {
                event.preventDefault(); // Prevent the browser's default context menu
            });
        }
    }

    // eslint-disable-next-line @typescript-eslint/member-ordering
    getMapExtendCoordinate = fromEventPattern(
        (handler) => this.dragBoxInteraction.on("boxend", handler),
        (handler) => this.dragBoxInteraction.un("boxend", handler)
    ) as Observable<DragBoxEvent>;

    getMapExtendCoordinateFromRightDrag = fromEventPattern(
        (handler) => this.dragBoxRightInteraction.on("boxend", handler),
        (handler) => this.dragBoxRightInteraction.un("boxend", handler)
    ) as Observable<DragBoxEvent>;

    // eslint-disable-next-line @typescript-eslint/member-ordering
    getMapSelectCoordinate = fromEventPattern(
        (handler) => this.map.on("singleclick", handler),
        (handler) => this.map.un("singleclick", handler)
    ) as Observable<MapBrowserEvent<UIEvent>>;

    addCreateMarkerClickListener() {
        if (this.mapCreateMarkerEventKey) {
            this.stopCreateMarkerClickListener();
        }

        this.mapCreateMarkerEventKey = this.map.on("click", (event) => {
            const coordinates = event.coordinate;
            const newMarkerId = 0;
            const markerStyleCallback = (feature: Feature) => {
                return new Style({
                    image: new Icon({
                        src: "assets/images/icons/map/pin.svg",
                    }),
                });
            };
            const newMarkerFeature = new Feature({
                geometry: new Point(coordinates),
            });

            this.removeFeatureById(newMarkerId, this.markersLayer);

            newMarkerFeature.setId(newMarkerId);
            newMarkerFeature.setStyle(markerStyleCallback);
            this.markersLayer.getSource().addFeature(newMarkerFeature);

            const mapObjectDetails: MapObjectDetailsType = {
                coordinate: coordinates,
                type: MapPopupEnum.CreateEditMarker,
                data: {
                    geometry: { coordinates },
                    id: newMarkerId,
                },
            };

            this.zone.run((_) => {
                this.store.dispatch(
                    MapUiActions.selectMapObject({
                        mapObjectDetails,
                    })
                );
            });
        });
    }

    stopCreateMarkerClickListener() {
        unByKey(this.mapCreateMarkerEventKey);
    }

    addSelectInteractionClickListener() {
        if (this.mapSelectInteractionClickEventKey) {
            this.stopSelectInteractionClickListener();
        }

        this.mapSelectInteractionClickEventKey = this.map.on(
            "click",
            (event) => {
                if (this.selectInteraction !== null) {
                    this.map.removeInteraction(this.selectInteraction);
                }

                this.selectInteraction = new Select({
                    condition: click,
                    layers: (layer) => {
                        return layer.get("selectable") === true;
                    },
                    style: (feature: Feature, resolution: number) => {
                        return new Style({
                            stroke: new Stroke({
                                color: "#ffff00",
                                width: 2,
                            }),
                            text: new Text({
                                font: "12px sans-serif",
                                fill: new Fill({ color: "#fff" }),
                                text: ((f: Feature, r: number) => {
                                    let text = f.getProperties().DN + "";
                                    if (r > 20) {
                                        text = "";
                                    }
                                    return text;
                                })(feature, resolution),
                            }),
                        });
                    },
                });

                this.map.addInteraction(this.selectInteraction);

                this.selectInteraction.on("select", (e: SelectEvent) => {
                    if (e.target.getFeatures().getLength()) {
                        this.selectedFeatures.next(e.target.getFeatures());
                    }
                });
            }
        );
    }

    stopSelectInteractionClickListener() {
        unByKey(this.mapSelectInteractionClickEventKey);
        if (this.selectInteraction !== null) {
            this.map.removeInteraction(this.selectInteraction);
        }
    }

    showOnLeftThematicLayer(
        layerName: string,
        layerMapToolLabel: string,
        params?: WMSLayerClientParamsType,
        map?: string
    ) {
        let formattedDate;
        const wms = this.leftThematicLayer_.getSource() as TileWMS;
        wms.clear();
        const wmsLayerParams: WMSLayerDefaultParamsType &
            WMSLayerClientParamsType = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = layerName;

        if (map) {
            wmsLayerParams.MAP = `${this.serverMapPath}${map}`;
        }

        wms.updateParams(wmsLayerParams);

        this.centreThematicLayerVisibility(false);
        this.leftThematicLayerVisibility(true);
        this.leftThematicLayer_.set("name", layerMapToolLabel);

        this.leftThematicLayer_.setProperties({ date: "" });
        if (params.date) {
            formattedDate = moment(params.date).format("YYYY-MM-DD");
            this.leftThematicLayer_.setProperties({ date: formattedDate });
        }

        this.leftThematicLayer_.setProperties({
            element: params.element ?? "",
        });
    }

    showOnRightThematicLayer(
        layerName: string,
        layerMapToolLabel: string,
        params?: WMSLayerClientParamsType,
        map?: string
    ) {
        let formattedDate;
        const wms = this.rightThematicLayer_.getSource() as TileWMS;
        wms.clear();

        const wmsLayerParams: WMSLayerDefaultParamsType &
            WMSLayerClientParamsType = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = layerName;

        if (map) {
            wmsLayerParams.MAP = `${this.serverMapPath}${map}`;
        }

        wms.updateParams(wmsLayerParams);

        this.centreThematicLayerVisibility(false);
        this.rightThematicLayerVisibility(true);
        this.rightThematicLayer_.set("name", layerMapToolLabel);

        this.rightThematicLayer_.setProperties({ date: "" });
        if (params.date) {
            formattedDate = moment(params.date).format("YYYY-MM-DD");
            this.rightThematicLayer_.setProperties({ date: formattedDate });
        }

        this.rightThematicLayer_.setProperties({
            element: params.element ?? "",
        });
    }

    showOnCentreThematicLayer(
        layerName: string,
        layerMapToolLabel: string,
        params?: WMSLayerClientParamsType,
        map?: string
    ) {
        let formattedDate;
        const wms = this.centreThematicLayer_.getSource() as TileWMS;
        wms.clear();
        const wmsLayerParams: WMSLayerDefaultParamsType &
            WMSLayerClientParamsType = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = layerName;

        if (map) {
            wmsLayerParams.MAP = `${this.serverMapPath}${map}`;
        }

        wms.updateParams(wmsLayerParams);

        this.leftThematicLayerVisibility(false);
        this.rightThematicLayerVisibility(false);
        this.centreThematicLayerVisibility(true);
        this.centreThematicLayer_.set("name", layerMapToolLabel);

        this.centreThematicLayer_.setProperties({ date: "" });
        if (params.date) {
            formattedDate = moment(params.date).format("YYYY-MM-DD");
            this.centreThematicLayer_.setProperties({ date: formattedDate });
        }

        this.centreThematicLayer_.setProperties({
            element: params.element ?? "",
        });
    }

    showOnTechnofarmLayer(
        childLayers: MapLayerType<TFMapLayerAttributesType>[],
        params?: WMSLayerTechnofarmParamsType,
        mapFilePath?: string
    ) {
        if (mapFilePath) {
            this.technofarmLayer.setMapFilePath(mapFilePath, false);
        }

        this.technofarmLayer.setChildLayers(childLayers, false);
        this.technofarmLayer.setRequestParams(params);
        this.technofarmLayer.setStyle(
            this.generateTechnofarmLayerStyleFunction(childLayers)
        );

        this.technofarmLayerVisibility(true);
    }

    showOnTechnofarmMapcacheLayer(
        technofarmMapcacheLayer: MvtLayer,
        childLayers: MapLayerType[],
        params?: WMSLayerTechnofarmParamsType,
        mapFilePath?: string
    ) {
        if (mapFilePath) {
            technofarmMapcacheLayer.setMapFilePath(mapFilePath, false);
        }

        technofarmMapcacheLayer.setChildLayers(childLayers, false);
        technofarmMapcacheLayer.setRequestParams({
            ...params,
            SERVICE: "WMTS",
            REQUEST: "GetTile",
        });
        technofarmMapcacheLayer.setStyle(
            this.generateTechnofarmLayerStyleFunction(childLayers)
        );
        technofarmMapcacheLayer.setVisible(true);

        this.technofarmMapcacheLayerVisibility(true);
    }

    clearTechnofarmLayers(layers: Array<TileLayer<TileWMS>>) {
        layers.forEach((layer) => {
            const wms = layer.getSource() as TileWMS;
            const wmsLayerParams: WMSLayerDefaultParamsType = {
                ...this.wmsLayerDefaultParams,
            };
            wmsLayerParams.LAYERS = "";
            wms.clear();
            layer.setProperties({ childLayers: [] });
            layer.setVisible(false);
            wms.updateParams(wmsLayerParams);
        });
    }

    clearMvtLayers(layers: Array<MvtLayer>) {
        layers.forEach((layer) => {
            layer.clear();
            layer.setVisible(false);
        });
    }

    showRgbByDateLayer(indexDate: string) {
        indexDate = moment(indexDate).format("YYYY-MM-DD");
        const wms = this.rgbByDateLayer_.getSource() as TileWMS;
        const bufferedExtent = buffer(
            this.authService.organizationExtent(),
            100
        );

        wms.setUrl(
            `${this.mapcacheUrl}/gmaps/sentinel_${indexDate}@g/{z}/{x}/{y}.png`
        );
        this.rgbByDateLayer_.setExtent(bufferedExtent);
        this.rgbByDateLayerVisibility(true);
        this.rgbByDateLayer.setProperties({ date: indexDate });
    }

    showSoilGridLayer(
        mapFilename?: string,
        params?: SoilGridTrackLayersParamsType,
        classGroup: SoilGridLayerClassGroupStyleEnum = SoilGridLayerClassGroupStyleEnum.WithGridColor
    ) {
        const wms = this.soilGridLayer_.getSource() as TileWMS;
        const wmsLayerParams: Partial<WMSLayerDefaultParamsType> = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = MapLayersEnum.SoilGrid;
        wmsLayerParams.CLASSGROUP = classGroup;
        wmsLayerParams.MAP = `${this.serverMapPath}${mapFilename}.map`;
        this.soilGridLayer_.setVisible(false);
        wms.updateParams(wmsLayerParams);
        this.soilGridLayerVisibility(true);
        this.soilGridLayer_.set("name", `Sampling grid`);
    }

    showSoilTrackLayer(
        mapFilename?: string,
        params?: SoilGridTrackLayersParamsType
    ) {
        const wms = this.soilTrackLayer_.getSource() as TileWMS;
        const wmsLayerParams: Partial<WMSLayerDefaultParamsType> = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = MapLayersEnum.SoilTrack;
        wmsLayerParams.MAP = `${this.serverMapPath}${mapFilename}.map`;
        this.soilTrackLayer_.setVisible(false);
        wms.updateParams(wmsLayerParams);
        this.soilTrackLayerVisibility(true);
        this.soilTrackLayer_.set("name", `Sampling track`);
    }

    addSoilByElementPHLayer(params: WMSLayerClientParamsType) {
        this.soilByElementPhLayer_.getSource().clear();
        params.element = ElementEnum.PH;
        const wms = this.soilByElementPhLayer_.getSource() as TileWMS;
        const wmsLayerParams: Partial<WMSLayerDefaultParamsType> = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = MapLayersEnum.SoilByElementPH;
        wmsLayerParams.MAP = `${this.serverMapPath}soil.map`;
        this.soilByElementPhLayer_.setVisible(false);
        wms.updateParams(wmsLayerParams);
        this.soilByElementPhLayer_.set("disable", false);
        this.soilByElementPhLayer_.set("name", ElementEnum.PH);
    }

    addSoilByElementP2O5Layer(params: WMSLayerClientParamsType) {
        this.soilByElementP2O5Layer_.getSource().clear();
        params.element = ElementEnum.P2O5;
        const wms = this.soilByElementP2O5Layer_.getSource() as TileWMS;
        const wmsLayerParams: Partial<WMSLayerDefaultParamsType> = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = MapLayersEnum.SoilByElementP2O5;
        wmsLayerParams.MAP = `${this.serverMapPath}soil.map`;
        this.soilByElementP2O5Layer_.setVisible(false);
        wms.updateParams(wmsLayerParams);
        this.soilByElementP2O5Layer_.set("disable", false);
        this.soilByElementP2O5Layer_.set("name", ElementEnum.P2O5);
    }

    addSoilByElementTMNLayer(params: WMSLayerClientParamsType) {
        this.soilByElementTMNLayer_.getSource().clear();
        params.element = ElementEnum.TMN;
        const wms = this.soilByElementTMNLayer_.getSource() as TileWMS;
        const wmsLayerParams: Partial<WMSLayerDefaultParamsType> = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = MapLayersEnum.SoilByElementTMN;
        wmsLayerParams.MAP = `${this.serverMapPath}soil.map`;
        this.soilByElementTMNLayer_.setVisible(false);
        wms.updateParams(wmsLayerParams);
        this.soilByElementTMNLayer_.set("disable", false);
        this.soilByElementTMNLayer_.set("name", ElementEnum.TMN);
    }

    addSoilByElementK2OLayer(params: WMSLayerClientParamsType) {
        this.soilByElementK2OLayer_.getSource().clear();
        params.element = ElementEnum.K2O;
        const wms = this.soilByElementK2OLayer_.getSource() as TileWMS;
        const wmsLayerParams: Partial<WMSLayerDefaultParamsType> = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };
        wmsLayerParams.LAYERS = MapLayersEnum.SoilByElementK2O;
        wmsLayerParams.MAP = `${this.serverMapPath}soil.map`;
        this.soilByElementK2OLayer_.setVisible(false);
        wms.updateParams(wmsLayerParams);
        this.soilByElementK2OLayer_.set("disable", false);
        this.soilByElementK2OLayer_.set("name", ElementEnum.K2O);
    }

    showSoilElementLayer(element: string) {
        this.map
            .getLayers()
            .getArray()
            .filter(
                (layer) =>
                    layer instanceof TileLayer &&
                    layer.getSource() instanceof TileWMS
            )
            .forEach((layer: TileLayer<TileSource>) => {
                const currentLayer = layer.getSource();
                const layerParams = (currentLayer as TileWMS).getParams()
                    .LAYERS;
                if (
                    layerParams === MapLayersEnum.SoilByElementPH ||
                    layerParams === MapLayersEnum.SoilByElementK2O ||
                    layerParams === MapLayersEnum.SoilByElementTMN ||
                    layerParams === MapLayersEnum.SoilByElementP2O5
                ) {
                    if (layer.getProperties().name === element) {
                        layer.setVisible(true);
                    } else {
                        layer.setVisible(false);
                    }
                }
                return layer.getSource() as TileWMS;
            });
    }

    showFieldsBoundariesLayer(params?: WMSLayerClientParamsType) {
        this.fieldsBoundariesLayer_.setRequestParams({
            ...this.fieldsBoundariesLayer_.params?.requestParams,
            ...params,
        });

        this.fieldsBoundariesLayerVisibility(true);
    }

    showSoilVraLayer(params?: WMSLayerClientParamsType) {
        const wms = this.soilVraWmsLayer_.getSource() as TileWMS;
        wms.clear();

        const wmsLayerParams: WMSLayerDefaultParamsType &
            WMSLayerClientParamsType = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };

        wmsLayerParams.LAYERS = MapLayersEnum.Vra;
        wms.updateParams(wmsLayerParams);
        this.soilVraWmsLayerVisibility(true);
    }

    showPlotsByCropLayer(params?: WMSLayerClientParamsType) {
        const wms = this.plotsByCropLayer_.getSource() as TileWMS;
        wms.clear();

        const wmsLayerParams: WMSLayerDefaultParamsType &
            WMSLayerClientParamsType = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };

        wmsLayerParams.LAYERS = MapLayersEnum.LayerPlotsByCrop;

        wms.updateParams(wmsLayerParams);
        this.plotsByCropLayerVisibility(true);
    }

    getSelectedFeatures(layer: VectorLayer | VectorImage): Array<Feature> {
        return layer.getSource().getFeatures();
    }

    addFeatures(
        layer: VectorLayer | VectorImage,
        features: Array<Feature>,
        iconStyleCallBack?: (feature: Feature) => Style
    ): void {
        let layerSource: VectorSource;

        layer.getSource() instanceof Cluster
            ? (layerSource = (
                  layer.getSource() as Cluster<Feature>
              ).getSource())
            : (layerSource = layer.getSource());

        layerSource.clear();

        const selectedFeaturesGids: Array<number | string> =
            this.getSelectedFeatures(layer).map((feature: Feature) => {
                return feature.getId();
            });
        const newFeatues: Feature[] = [];
        features.forEach((feature: Feature) => {
            if (selectedFeaturesGids.includes(feature.getId())) {
                return;
            }

            if (iconStyleCallBack) {
                feature.setStyle(iconStyleCallBack);
            }
            newFeatues.push(feature);
        });
        layerSource.addFeatures(newFeatues);
    }

    removeFeatureById(featureId: number | string, layer: VectorLayer) {
        const layerSource = layer.getSource();
        const featureToRemove = layerSource.getFeatureById(
            featureId
        ) as Feature;

        if (featureToRemove) {
            layerSource.removeFeature(featureToRemove);
        }
    }

    bingLayerVisibility(value: boolean) {
        this.bingLayer.setVisible(value);
    }

    fieldsBoundariesLayerVisibility(value: boolean) {
        this.fieldsBoundariesLayer_.setVisible(value);
    }

    rgbByDateLayerVisibility(value: boolean) {
        this.rgbByDateLayer_.setVisible(value);
        this.rgbByDateLayer_.set("disable", !value);
    }

    soilSamplesLayerVisibility(value: boolean) {
        this.soilSamplesLayer_.setVisible(value);
    }

    selectedPlotLayerVisibility(value: boolean) {
        this.selectedPlotLayer_.setVisible(value);
    }

    technofarmLayerVisibility(value: boolean) {
        this.technofarmLayer_.setVisible(value);
    }

    technofarmMapcacheLayerVisibility(value: boolean) {
        this.technofarmMapcacheLayer_.setVisible(value);
    }

    soilGridLayerVisibility(value: boolean) {
        this.soilGridLayer_.setVisible(value);
        this.soilGridLayer_.set("disable", !value);
    }

    soilTrackLayerVisibility(value: boolean) {
        this.soilTrackLayer_.setVisible(value);
        this.soilTrackLayer_.set("disable", !value);
    }

    soilByElementPhLayerVisibility(value: boolean) {
        this.soilByElementPhLayer_.setVisible(value);
        this.soilByElementPhLayer_.set("disable", !value);
    }

    soilByElementTMNLayerVisibility(value: boolean) {
        this.soilByElementTMNLayer_.setVisible(value);
        this.soilByElementTMNLayer_.set("disable", !value);
    }

    soilByElementP2O5LayerVisibility(value: boolean) {
        this.soilByElementP2O5Layer_.setVisible(value);
        this.soilByElementP2O5Layer_.set("disable", !value);
    }

    soilByElementK2OLayerVisibility(value: boolean) {
        this.soilByElementK2OLayer_.setVisible(value);
        this.soilByElementK2OLayer_.set("disable", !value);
    }

    externalTechnofarmMapcacheLayerVisibility(value: boolean) {
        this.externalTechnofarmMapcacheLayer_.setVisible(value);
    }

    markersLayerVisibility(value: boolean) {
        this.markersLayer_.setVisible(value);
    }

    measurementLayerVisibiliy(value: boolean) {
        this.measurementLayer_.setVisible(value);
    }

    markersLayerDisable(value: boolean) {
        this.markersLayer_.set("disable", value);
    }

    weatherStationsLayerVisibility(value: boolean) {
        this.weatherStationsLayer_.setVisible(value);
    }

    weatherStationsLayerDisable(value: boolean) {
        this.weatherStationsLayer_.set("disable", value);
    }

    platformsLayerVisibility(value: boolean) {
        this.platformsLayer_.setVisible(value);
    }

    platformsLayerDisable(value: boolean) {
        this.platformsLayer_.set("disable", value);
    }

    platformsEmptyLayerVisibility(value: boolean) {
        this.platformsEmptyLayer_.setVisible(value);
    }

    pivotsActiveLayerVisibility(value: boolean) {
        this.pivotsActiveLayer_.setVisible(value);
    }

    pivotsIrrLayerVisibility(value: boolean) {
        this.pivotsIrrLayer_.setVisible(value);
    }

    pivotsPALayerVisibility(value: boolean) {
        this.pivotsPALayer_.setVisible(value);
    }

    pivotsOffLayerVisibility(value: boolean) {
        this.pivotsOffLayer_.setVisible(value);
    }

    pivotsMovementLayerVisibility(value: boolean) {
        this.pivotsMovementLayer_.setVisible(value);
    }

    pivotsWarningLayerVisibility(value: boolean) {
        this.pivotsWarningLayer_.setVisible(value);
    }

    pivotsVisibility(value: boolean) {
        this.pivotsActiveLayer_.setVisible(value);
        this.pivotsIrrLayer_.setVisible(value);
        this.pivotsPALayer_.setVisible(value);
        this.pivotsOffLayer_.setVisible(value);
        this.pivotsMovementLayer_.setVisible(value);
        this.pivotsWarningLayer_.setVisible(value);
    }

    currentPivotsPositionLayerVisibility(value: boolean) {
        this.pivotsCurrentPositionLayer_.setVisible(value);
    }

    currentPivotsPositionLayerDisable(value: boolean) {
        this.pivotsCurrentPositionLayer_.set("disable", value);
    }

    irrigationTaskPositionLayerVisibility(value: boolean) {
        this.irrigationTasksPositionLayer_.setVisible(value);
    }

    irrigationTaskSegmentLayerVisibility(value: boolean) {
        this.irrigationTasksSegmentPositionLayer_.setVisible(value);
    }

    irrigationTaskPositionLayerDisable(value: boolean) {
        this.irrigationTasksPositionLayer_.set("disable", value);
    }

    irrigationTasksTransportationLayerVisibility(value: boolean) {
        this.irrigationTasksTransportationLayer_.setVisible(value);
    }

    irrigationTasksTransportationLayerDisable(value: boolean) {
        this.irrigationTasksTransportationLayer_.set("disable", value);
    }

    machinesLayerVisibility(value: boolean) {
        this.machinesLayer_.setVisible(value);
    }

    machinesLayerDisable(value: boolean) {
        this.machinesLayer_.set("disable", value);
    }

    machinesVisibility(value: boolean) {
        this.machinesOffLayer_.setVisible(value);
        this.machinesIdleLayer_.setVisible(value);
        this.machinesMovingLayer_.setVisible(value);
        this.machinesOfflineLayer_.setVisible(value);
        this.machinesEventsTrackLayer_.setVisible(value);
        this.machinesTrackLayer_.setVisible(value);
        this.machinesEventsWorkAreaLayer_.setVisible(value);
    }

    irrigationTasksLayersVisibility(value: boolean) {
        this.irrigationTasksLayer_.setVisible(value);
        this.irrigationTasksPositionLayer_.setVisible(value);
        this.irrigationTasksTransportationLayer_.setVisible(value);
    }

    machinesOffLayerVisibility(value: boolean) {
        this.machinesOffLayer_.setVisible(value);
    }

    machinesIdleLayerVisibility(value: boolean) {
        this.machinesIdleLayer_.setVisible(value);
    }

    machinesMovingLayerVisibility(value: boolean) {
        this.machinesMovingLayer_.setVisible(value);
    }

    machinesOfflineLayerVisibility(value: boolean) {
        this.machinesOfflineLayer_.setVisible(value);
    }

    machinesTrackLayerVisibility(value: boolean) {
        this.machinesTrackLayer_.setVisible(value);
    }

    machinesEventsTrackLayerVisibility(value: boolean) {
        this.machinesEventsTrackLayer_.setVisible(value);
    }

    machinesEventsWorkAreaLayerVisibility(value: boolean) {
        this.machinesEventsWorkAreaLayer_.setVisible(value);
    }

    irrigationTasksVisibility(value: boolean) {
        this.irrigationTasksLayer_.setVisible(value);
    }

    soilVraVectorLayerVisibility(value: boolean) {
        this.soilVraVectorLayer_.setVisible(value);
        this.soilVraVectorLayer_.set("disable", !value);
        this.soilVraVectorLayer_.set("selectable", value);
    }

    soilVraWmsLayerVisibility(value: boolean) {
        this.soilVraWmsLayer_.setVisible(value);
        this.soilVraWmsLayer_.set("disable", !value);
    }

    hideAllThematicWmsLayers() {
        this.leftThematicLayerVisibility(false);
        this.rightThematicLayerVisibility(false);
        this.centreThematicLayerVisibility(false);
    }

    leftThematicLayerVisibility(value: boolean) {
        this.leftThematicLayer_.setVisible(value);
        this.leftThematicLayer_.set("disable", !value);
    }

    rightThematicLayerVisibility(value: boolean) {
        this.rightThematicLayer_.setVisible(value);
        this.rightThematicLayer_.set("disable", !value);
    }

    centreThematicLayerVisibility(value: boolean) {
        this.centreThematicLayer_.setVisible(value);
        this.centreThematicLayer_.set("disable", !value);
    }

    plotsByCropLayerVisibility(value: boolean) {
        this.plotsByCropLayer_.setVisible(value);
        this.plotsByCropLayer_.set("disable", !value);
    }

    geolocationLayerVisibility(value: boolean) {
        this.geolocationLayer_.setVisible(value);
    }

    setLayerVisibilityById(layerId: string, visible: boolean) {
        const layer = this.map
            .getLayers()
            .getArray()
            .find((layer) => layer.get("id") === layerId);
        layer.setVisible(visible);
    }

    surveysLayerVisibility(value: boolean) {
        this.surveysLayer_.setVisible(value);
    }

    kvsManageContractsLayerVisibility(value: boolean) {
        this.kvsManageConractsLayer_.setVisible(value);
    }

    addMarkers(
        markers: any[],
        iconStyleCallBack: (feature: Feature) => Style,
        layer: VectorLayer
    ) {
        const features: Feature[] = [];

        markers.forEach((marker: any) => {
            const feature = new Feature({
                geometry: new Point(marker.geometry.coordinates),
            });
            features.push(feature);

            if (marker.properties) {
                feature.setProperties(marker.properties);
            }
        });

        const vectorSource = new VectorSource({
            features,
        });

        layer.setSource(vectorSource);
        layer.setStyle(iconStyleCallBack);
    }

    addPlatformsEmptyLayer(features: Feature[]) {
        const vectorSource = new VectorSource({
            features,
        });

        const clusterSource = new Cluster({
            source: vectorSource,
        });

        const clusterLayer = new VectorLayer({
            source: clusterSource,
        });

        this.map.addLayer(clusterLayer);
    }

    transformCoordinate(from: string, to: string, coordinate: Coordinate) {
        return proj4(from, to, coordinate);
    }

    clearMachinesEventsTracks() {
        this.machinesEventsTrackLayer.getSource().clear();
    }

    clearMachinesEventsWorkArea() {
        this.machinesEventsWorkAreaLayer.getSource().clear();
    }

    clearIrrigationTasksTransportationPaths() {
        this.irrigationTasksTransportationLayer.getSource().clear();
    }

    createCircleFeature(
        lon: string | number,
        lat: string | number,
        radius: number
    ): Feature {
        const view = this.map.getView();
        const projection = view.getProjection();
        const resolutionAtEquator = view.getResolution();
        const center = this.map.getView().getCenter();
        const pointResolution = getPointResolution(
            projection,
            resolutionAtEquator,
            center
        );
        const resolutionFactor = resolutionAtEquator / pointResolution;
        const calculatedRadius =
            ((radius * 1000) / METERS_PER_UNIT.m) * resolutionFactor;

        const transformCoordinates = transform(
            [parseFloat(lon as string), parseFloat(lat as string)],
            "EPSG:4326",
            "EPSG:3857"
        );
        const circle = new Circle(transformCoordinates, calculatedRadius);
        const circleStyles = new Style({
            fill: new Fill({
                color: "rgba(255, 255, 255, 0.3)",
            }),
            stroke: new Stroke({
                width: 1,
                color: "rgba(255, 255, 255, 0.8)",
            }),
        });
        const circleFeature = new Feature(circle);
        circleFeature.setStyle(circleStyles);

        return circleFeature;
    }

    getExtent(features: Feature[]) {
        if (features.length) {
            return features.reduce((prev: Extent, curr: Feature) => {
                return extend(prev, curr.getGeometry().getExtent());
            }, features[0].getGeometry().getExtent());
        }
    }

    geoJSONObjectToOl(
        geojson: GeoJSONFeature<GeoJSONGeometry> | GeoJSONGeometry,
        transfromToProj: string = null
    ) {
        let props = {};

        if (transfromToProj) {
            props = { featureProjection: transfromToProj };
        }

        if (geojson?.type === "Feature") {
            return new GeoJSON(props).readFeature(geojson) as Feature<Geometry>;
        }

        const olGeom = new GeoJSON(props).readGeometry(geojson) as Geometry;

        return new Feature(olGeom);
    }

    convertDegToRad(deg: number) {
        return (deg * Math.PI * 2) / 360;
    }

    setMachineCurrentTrackFeatureStyles(
        machineTracks: MachineTrackType[]
    ): Feature[] {
        const tracksWithStyles = machineTracks.map(
            (machineTrack: MachineTrackType) => {
                const feature = machineTrack.feature;
                const trackColorRange = machineTrack.props.colorRange;
                const lineString = (
                    feature.getGeometry() as MultiLineString
                ).getLineString(0);
                const styles = new FlowLine({
                    visible: false,
                    lineCap: "round",
                    color: (feature: Feature, step: number) => {
                        const segment: number[] = [];
                        lineString.getCoordinateAt(step, segment);
                        const [lat, lon, value, timestamp] = segment;

                        return this.getColorFromRange(trackColorRange, value);
                    },
                    width: 5,
                });

                styles.setGeometry(lineString);
                feature.setStyle(styles);

                return {
                    ...machineTrack,
                    feature,
                };
            }
        );

        return tracksWithStyles.map((machineTrack) => machineTrack.feature);
    }

    generateTechnofarmLayerStyleFunction(
        childLayers: MapLayerType<TFMapLayerAttributesType>[]
    ): (feature: Feature) => Style {
        const hiddenFillOptionsByStyleLayerId =
            this.getHiddenFillOptionsByStyleLayerId(childLayers);

        const highlightStyle = new Style({
            fill: new Fill({
                color: "rgb(51, 176, 176, 0.5)",
            }),
            stroke: new Stroke({
                color: "rgb(153, 215, 215)",
                width: 2,
            }),
            zIndex: 2,
        });

        const selectedStyle = new Style({
            stroke: new Stroke({
                width: 5,
                color: "#e3c524",
            }),
            fill: new Fill({
                color: "rgba(227,197,36,0.5)",
            }),
            zIndex: 3,
        });

        return (feature: Feature) => {
            const properties = feature.getProperties();

            const opacity = (100 - (properties.transparency ?? 0)) / 100;
            const borderOnly = properties.border_only?.toLowerCase() === "true";

            const fillColorHex =
                properties.fill_color?.length > 0
                    ? properties.fill_color
                    : "#000000";
            const hiddenFillOptions =
                hiddenFillOptionsByStyleLayerId[properties.style_layer_id];
            const featureFillColumnValue =
                properties[hiddenFillOptions?.fillColumnName]?.length > 0
                    ? properties[hiddenFillOptions?.fillColumnName]
                    : null;

            const isHiddenFill =
                featureFillColumnValue !== undefined &&
                hiddenFillOptions?.hiddenValues?.includes(
                    featureFillColumnValue
                );

            const fillColorRGB = [
                ...this.helperService.hex2RgbArray(fillColorHex),
                borderOnly || isHiddenFill || !properties.fill_color?.length
                    ? 0
                    : opacity,
            ];

            const strokeColorHex = properties.border_color
                ? properties.border_color
                : "#000000";
            const strokeColorRGB =
                this.helperService.hex2RgbArray(strokeColorHex);

            const layerFeatureId = properties.layer_id + "_" + feature.getId();
            if (
                this.technofarmHoveredFeaturesIds.includes(layerFeatureId) &&
                !this.technofarmSelectedFeatureIds.includes(layerFeatureId)
            ) {
                return highlightStyle;
            }

            if (this.technofarmSelectedFeatureIds.includes(layerFeatureId)) {
                return selectedStyle;
            }
            if (feature.getGeometry().getType() === "Point") {
                return new Style({
                    image: new CircleStyle({
                        radius: 5,
                        fill: new Fill({
                            color: fillColorRGB,
                        }),
                        stroke: new Stroke({
                            color: [...strokeColorRGB],
                            width: properties.border_width ?? 1,
                        }),
                    }),
                    zIndex: 1,
                });
            }
            return new Style({
                fill: new Fill({
                    color: fillColorRGB,
                }),
                stroke: new Stroke({
                    color: [...strokeColorRGB],
                    width: properties.border_width ?? 1,
                }),
                text: new Text({
                    font: (properties.label_size ?? "12") + "px sans-serif",

                    placement: "point",
                    fill: new Fill({
                        color: "#ffffff",
                    }),
                    stroke: new Stroke({
                        color: "#000000",
                        width: 3,
                    }),
                    text: properties.label ?? "",
                }),
                zIndex: 1,
            });
        };
    }

    private initSoilSamplesLayer() {
        this.soilSamplesLayer_ = new VectorLayer({
            source: new VectorSource({
                features: [],
            }),
            style: (feature: Feature) => {
                const size = feature.getId();
                return new Style({
                    image: new CircleStyle({
                        radius: 10,
                        stroke: new Stroke({
                            color: "#fff",
                        }),
                        fill: new Fill({
                            color: "#41c0ea",
                        }),
                    }),
                    text: new Text({
                        text: size.toString(),
                        fill: new Fill({
                            color: "#303946",
                        }),
                    }),
                });
            },
        });
        this.soilSamplesLayer_.setProperties({
            name: "Soil Samples Layer",
            id: MapLayersIdEnum.SoilSamplesLayerId,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        this.soilSamplesLayer_.setZIndex(5);
        this.addLayer(this.soilSamplesLayer_);
    }

    private initLeftThematicLayer() {
        this.leftThematicLayer_ = this.createWmsLayer(
            this.wmsServer,
            {},
            this.authService.organizationExtent()
        );
        this.leftThematicLayer_.setProperties({
            id: MapLayersIdEnum.LeftLayerId,
            name: "",
            labelIndex: "Left",
            disable: true,
            visibleInMapTools: true,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
            isSystem: true,
        });
        this.addLayer(this.leftThematicLayer_);
    }

    private initRightThematicLayer() {
        this.rightThematicLayer_ = this.createWmsLayer(
            this.wmsServer,
            {},
            this.authService.organizationExtent()
        );
        this.rightThematicLayer_.setProperties({
            id: MapLayersIdEnum.RightLayerId,
            name: "",
            labelIndex: "Right",
            disable: true,
            visibleInMapTools: true,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
            isSystem: true,
        });
        this.addLayer(this.rightThematicLayer_);
    }

    private initCentreThematicLayer() {
        this.centreThematicLayer_ = this.createWmsLayer(
            this.wmsServer,
            {},
            this.authService.organizationExtent()
        );
        this.centreThematicLayer_.setProperties({
            id: MapLayersIdEnum.CentreLayerId,
            name: "",
            disable: true,
            visibleInMapTools: true,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
            isSystem: true,
        });
        this.addLayer(this.centreThematicLayer_);
    }

    private async initTechnofarmLayer() {
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const self = this;
        this.technofarmLayer_ = new MvtLayer(
            {},
            {
                // The access token is added to the URL on every request.
                buildUrl: function (params: MvtLayerParamsType) {
                    return this.buildUrl(params).map((url) => {
                        return `${url}&access_token=${self.authService.getAuthToken()}`;
                    });
                },
            }
        );
        this.technofarmLayer_.setProperties({
            id: MapLayersIdEnum.TechnofarmLayerId,
            name: "",
            disable: true,
            visibleInMapTools: false,
            persistable: true,
            forceHidden: false,
            hasChildWmsLayers: true,
            visible: false,
        });

        this.addLayer(this.technofarmLayer_);
    }

    private initTechnofarmMapcacheLayer() {
        this.technofarmMapcacheLayer_ = new MvtLayer(
            {},
            {
                // This is needed on to get the correct tile, the actual loading is happening in the tileLoadFunction
                buildUrl: (_params: MvtLayerParamsType) => {
                    return [`TILEMATRIX={z}&TILEROW={y}&TILECOL={x}`];
                },
                // This function resoleves the problem with showing multiple cached MVT layers
                // on the same layer.
                tileLoadFunction: (params: MvtLayerParamsType) => {
                    const urlTemplates = params.childLayers
                        ?.filter((layer) => layer.visible)
                        ?.map((layer) => {
                            let concatEl = "?";
                            if (params.serverUrl.includes("?")) {
                                concatEl = "&";
                            }
                            return `${params.serverUrl}/wmts${concatEl}SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${layer.mvtLayerName}&TILEMATRIXSET=GoogleMapsCompatible&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&FORMAT=mvt`;
                        });
                    return (tile) => {
                        tile.setLoader(
                            function (extent, resolution, projection) {
                                // Get tile coordinates [z, x, y]
                                const tileCoord = tile.getTileCoord();
                                const z = tileCoord[0].toString(),
                                    x = tileCoord[1].toString(),
                                    y = tileCoord[2].toString();

                                // Create an array of promises for fetching and parsing each URL
                                const promises = urlTemplates?.map(
                                    (urlTemplate) => {
                                        const url = urlTemplate
                                            .replace("{z}", z)
                                            .replace("{x}", x)
                                            .replace("{y}", y);
                                        return fetch(url)
                                            .then((response) =>
                                                response.arrayBuffer()
                                            )
                                            .then((data) => {
                                                const format = tile.getFormat(); // ol/format/MVT configured as source format
                                                return format.readFeatures(
                                                    data,
                                                    {
                                                        extent: extent,
                                                        featureProjection:
                                                            projection,
                                                    }
                                                );
                                            })
                                            .catch((error) => {
                                                console.error(
                                                    `Error loading tile from ${url}:`,
                                                    error
                                                );
                                                return []; // Return empty array on error
                                            });
                                    }
                                );
                                if (!promises) {
                                    tile.setFeatures([]);
                                    return;
                                }
                                // Wait for all fetches to complete and merge the features
                                Promise.all(promises)
                                    .then((results) => {
                                        // Flatten the array of feature arrays into a single array
                                        const mergedFeatures = results.reduce(
                                            (acc, features) =>
                                                acc.concat(features),
                                            []
                                        );
                                        tile.setFeatures(mergedFeatures);
                                    })
                                    .catch((error) => {
                                        console.error(
                                            "Error processing multiple tile URLs:",
                                            error
                                        );
                                        tile.setFeatures([]);
                                    });
                            }
                        );
                    };
                },
            }
        );
        this.technofarmMapcacheLayer_.setMinZoom(13);
        this.technofarmMapcacheLayer_.setProperties({
            id: MapLayersIdEnum.TechnofarmMapcacheLayerId,
            name: "Technofarm mapcache layer",
            visible: true,
            disable: false,
            visibleInMapTools: false,
            persistable: true,
            forceHidden: false,
            hasChildWmsLayers: true,
            isSystem: true,
        });
        this.addLayer(this.technofarmMapcacheLayer_);
    }

    private initFieldsBoundariesLayer() {
        const childLayers: MapLayerType[] = [
            {
                id: MapLayersIdEnum.FieldsBoundariesLayerId,
                name: "layer_satellite_orders",
                mvtLayerName: "layer_satellite_orders",
                visible: true,
                disable: false,
                visibleInMapTools: false,
                persistable: false,
                forceHidden: false,
                labelIndex: "Fields",
                isSystem: true,
            },
        ];
        // eslint-disable-next-line @typescript-eslint/no-this-alias
        const self = this;
        this.fieldsBoundariesLayer_ = new MvtLayer(
            {
                mapFilePath: this.wmsLayerDefaultParams.MAP,
                serverUrl: this.wmsServer,
                childLayers,
            },
            {
                // The access token is added to the URL on every request.
                buildUrl: function (params: MvtLayerParamsType) {
                    return this.buildUrl(params).map((url) => {
                        return `${url}&access_token=${self.authService.getAuthToken()}`;
                    });
                },
            }
        );

        this.fieldsBoundariesLayer_.setProperties({
            id: MapLayersIdEnum.FieldsBoundariesLayerId,
            name: MapPopupEnum.Fields,
            disable: false,
            visibleInMapTools: true,
            persistable: true,
            forceHidden: false,
            hasChildWmsLayers: false,
            isSystem: true,
        });

        const styleCallback = (feature: Feature) => {
            if (
                this.fieldBoundariesSelectedFeatureGid ==
                feature.getProperties().gid
            ) {
                return new Style({
                    stroke: new Stroke({
                        width: 5,
                        color: "#e3c524",
                    }),
                    fill: new Fill({
                        color: "rgba(227,197,36,0.2)",
                    }),
                });
            } else {
                return new Style({
                    stroke: new Stroke({
                        width: 2,
                        color: "rgb(0 131 201)",
                    }),
                    fill: new Fill({
                        color: "rgba(255,255,255, 0)",
                    }),
                    text: new Text({
                        font: "12px sans-serif",
                        fill: new Fill({ color: "#FFFFFF" }),
                        stroke: new Stroke({
                            color: "#000000",
                            width: 3,
                        }),
                        text: ((f: Feature) => {
                            return f.getProperties().name;
                        })(feature),
                    }),
                });
            }
        };

        this.fieldsBoundariesLayer_.setStyle(styleCallback);
        this.addLayer(this.fieldsBoundariesLayer_);
    }

    private initRgbByDateLayer() {
        this.rgbByDateLayer_ = this.createTileLayer();
        this.rgbByDateLayer_.setProperties({
            id: MapLayersIdEnum.RgbByDateLayerId,
            name: "Real color",
            disable: true,
            visibleInMapTools: true,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
            isSystem: true,
        });
        this.addLayer(this.rgbByDateLayer_);
    }

    private initSoilGridLayer() {
        this.soilGridLayer_ = this.createWmsLayer(
            this.wmsServer,
            {},
            this.authService.organizationExtent()
        );
        this.soilGridLayer_.setProperties({
            id: MapLayersIdEnum.SoilGridLayer,
            name: "Soil grid",
            disable: true,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
        });
        this.addLayer(this.soilGridLayer_);
    }

    private initSoilTrackLayer() {
        this.soilTrackLayer_ = this.createWmsLayer(
            this.wmsServer,
            {},
            this.authService.organizationExtent()
        );
        this.soilTrackLayer_.setProperties({
            id: MapLayersIdEnum.SoilTrackLayer,
            name: "Soil track",
            disable: true,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
        });
        this.addLayer(this.soilTrackLayer_);
    }

    private initSoilByElementPHLayer() {
        this.soilByElementPhLayer_ = this.createWmsLayer(this.wmsServer);
        this.soilByElementPhLayer_.setProperties({
            id: MapLayersIdEnum.SoilByElementPHLayer,
            name: "pH",
            disable: true,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
        });
        this.addLayer(this.soilByElementPhLayer_);
    }

    private initSoilByElementP2O5Layer() {
        this.soilByElementP2O5Layer_ = this.createWmsLayer(this.wmsServer);
        this.soilByElementP2O5Layer_.setProperties({
            id: MapLayersIdEnum.SoilByElementP2O5Layer,
            name: "P2O5",
            disable: true,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
        });
        this.addLayer(this.soilByElementP2O5Layer_);
    }

    private initSoilByElementTMNLayer() {
        this.soilByElementTMNLayer_ = this.createWmsLayer(this.wmsServer);
        this.soilByElementTMNLayer_.setProperties({
            id: MapLayersIdEnum.SoilByElementTMNLayer,
            name: "TMN",
            disable: true,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
        });
        this.addLayer(this.soilByElementTMNLayer_);
    }

    private initSoilByElementK2OLayer() {
        this.soilByElementK2OLayer_ = this.createWmsLayer(this.wmsServer);
        this.soilByElementK2OLayer_.setProperties({
            id: MapLayersIdEnum.SoilByElementK2OLayer,
            name: "K2O",
            disable: true,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
        });
        this.addLayer(this.soilByElementK2OLayer_);
    }

    private initPlotsByCropLayer() {
        this.plotsByCropLayer_ = this.createWmsLayer(
            this.wmsServer,
            {},
            this.authService.organizationExtent()
        );
        this.plotsByCropLayer_.setProperties({
            id: MapLayersIdEnum.PlotsByCropId,
            name: "Plots by crop",
            disable: true,
            visibleInMapTools: true,
            persistable: true,
            forceHidden: false,
            hasChildWmsLayers: false,
            isSystem: true,
        });
        this.addLayer(this.plotsByCropLayer_);
    }

    showOnExternalTechnofarmMapcacheLayer(
        childLayers: MapLayerType[],
        params?: WMSLayerTechnofarmParamsType,
        map?: string
    ) {
        const wms = this.externalTechnofarmMapcacheLayer_.getSource();
        wms.clear();
        const wmsLayerParams: WMSLayerDefaultParamsType &
            WMSLayerTechnofarmParamsType = {
            ...this.wmsLayerDefaultParams,
            ...params,
        };

        this.externalTechnofarmMapcacheLayer_.setProperties({
            childLayers,
        });

        const layers = [
            ...new Set(
                childLayers
                    .filter((layer: MapLayerType) => layer.visible)
                    .map((layer: MapLayerType) => layer.wmsLayerName)
            ),
        ];

        wmsLayerParams.LAYERS = layers.toString();

        if (map) {
            wmsLayerParams.MAP = map;
        }

        this.externalTechnofarmMapcacheLayer_.setVisible(true);

        wms.updateParams(wmsLayerParams);
    }

    private initExternalTechnofarmMapcacheLayer() {
        this.externalTechnofarmMapcacheLayer_ = this.createWmsLayer(
            this.authService.getMapcacheUrl(),
            {},
            this.authService.defaultExtent()
        );
        this.externalTechnofarmMapcacheLayer_.setMinZoom(8);
        this.externalTechnofarmMapcacheLayer_.setProperties({
            id: MapLayersIdEnum.TechnofarmExternalMapcacheLayerId,
            name: "Technofarm external mapcache layer",
            disable: false,
            visibleInMapTools: false,
            persistable: true,
            forceHidden: false,
            hasChildWmsLayers: true,
        });

        this.addLayer(this.externalTechnofarmMapcacheLayer_);
    }

    private createTileLayer() {
        return new TileLayer({
            extent: this.defaultExtent,
            source: new XYZ({
                url: "",
            }),
            visible: false,
        });
    }

    private createWmsLayer(
        url: string,
        wmsParams: Partial<WMSLayerDefaultParamsType> = {},
        layerExtent?: Extent
    ) {
        return new TileLayer({
            ...(layerExtent && { extent: layerExtent }),
            source: new TileWMS({
                url,
                params: { ...this.wmsLayerDefaultParams, ...wmsParams },
                serverType: "mapserver",
                crossOrigin: "anonymous",
                tileLoadFunction: async (tile: ImageTile, url: string) => {
                    const token =
                        (await this.authService.getAccessToken()) ??
                        this.authService.getAuthToken();
                    const init = {
                        method: "GET",
                        headers: new Headers({
                            Authorization: `Bearer ${token}`,
                        }),
                    };

                    let response;
                    try {
                        response = await fetch(url, init);
                    } catch (error) {
                        tile.setState(TileState.ERROR);
                        console.error(error);
                        return;
                    }

                    if (!response.ok) {
                        tile.setState(TileState.ERROR);
                        return;
                    }
                    const imageData = await response.blob();
                    tile.setState(TileState.LOADED);
                    if (
                        ["application/vnd.ogc.se_xml", "text/html"].includes(
                            imageData.type
                        )
                    ) {
                        const error = await imageData.text();
                        console.error(error);
                        return;
                    }
                    (tile.getImage() as HTMLImageElement).src =
                        URL.createObjectURL(imageData);
                },
            }),
            visible: false,
        });
    }

    private initMarkersLayer() {
        this.markersLayer_ = this.createVectorLayer();
        this.markersLayer_.setProperties({
            id: MapLayersIdEnum.MarkersLayerId,
            name: "Markers",
            disable: false,
            visibleInMapTools: true,
            persistable: true,
            forceHidden: false,
            isSystem: true,
        });
        this.addLayer(this.markersLayer_);
    }

    private initWeatherStationsLayer() {
        const weatherStationStyleCallback = (feature: Feature) => {
            return new Style({
                image: new Icon({
                    src: "assets/images/icons/map/weather-station.svg",
                }),
            });
        };

        this.weatherStationsLayer_ = this.createVectorLayer();
        this.weatherStationsLayer_.setProperties({
            id: MapLayersIdEnum.WeatherStationsLayerId,
            name: "Weather stations",
            disable: false,
            visibleInMapTools: true,
            persistable: true,
            forceHidden: false,
            isSystem: true,
        });

        this.weatherStationsLayer_.setStyle(weatherStationStyleCallback);
        this.addLayer(this.weatherStationsLayer_);
    }

    private initPlatformsEmptyLayer() {
        this.platformsEmptyLayer_ = this.createClusterVectorLayer();
        this.platformsEmptyLayer_.setProperties({
            id: MapLayersIdEnum.PlatformsEmptyLayerId,
            name: "Platforms empty",
            disable: false,
            groupName: MapPopupEnum.Platforms,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initPivotsActiveLayer() {
        this.pivotsActiveLayer_ = this.createClusterVectorLayer();
        this.pivotsActiveLayer_.setProperties({
            id: MapLayersIdEnum.PivotsActiveLayerId,
            name: "Pivots active",
            disable: false,
            groupName: MapPopupEnum.Platforms,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initPivotsIrrLayer() {
        this.pivotsIrrLayer_ = this.createClusterVectorLayer();
        this.pivotsIrrLayer_.setProperties({
            id: MapLayersIdEnum.PivotsIrrLayerId,
            name: "Pivots irrigation",
            disable: false,
            groupName: MapPopupEnum.Platforms,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initPivotsOffLayer() {
        this.pivotsOffLayer_ = this.createClusterVectorLayer();
        this.pivotsOffLayer_.setProperties({
            id: MapLayersIdEnum.PivotsOffLayerId,
            name: "Pivots off",
            disable: false,
            groupName: MapPopupEnum.Platforms,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initPivotsPALayer() {
        this.pivotsPALayer_ = this.createClusterVectorLayer();
        this.pivotsPALayer_.setProperties({
            id: MapLayersIdEnum.PivotsPALayerId,
            name: "Pivots pressure alarm",
            disable: false,
            groupName: MapPopupEnum.Platforms,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initPivotsMovementLayer() {
        this.pivotsMovementLayer_ = this.createClusterVectorLayer();
        this.pivotsMovementLayer_.setProperties({
            id: MapLayersIdEnum.PivotsMovementLayerId,
            name: "Pivots movement",
            disable: false,
            groupName: MapPopupEnum.Platforms,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initPivotsWarningLayer() {
        this.pivotsWarningLayer_ = this.createClusterVectorLayer();
        this.pivotsWarningLayer_.setProperties({
            id: MapLayersIdEnum.PivotsWarningLayerId,
            name: "Pivots warning",
            disable: false,
            groupName: MapPopupEnum.Platforms,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initPlatformsLayer() {
        const layers = [
            this.platformsEmptyLayer_,
            this.pivotsActiveLayer_,
            this.pivotsIrrLayer_,
            this.pivotsOffLayer_,
            this.pivotsPALayer_,
            this.pivotsMovementLayer_,
            this.pivotsWarningLayer_,
        ];
        this.platformsLayer_ = this.createLayerGroup(layers);
        this.platformsLayer_.setProperties({
            id: MapLayersIdEnum.PlatformsLayerId,
            name: "Platforms",
            disable: false,
            visibleInMapTools: true,
            persistable: true,
            forceHidden: false,
            isSystem: true,
        });
        this.addLayer(this.platformsLayer_);
    }

    private initPivotsCurrentPositionLayer() {
        const style = () => {
            return new Style({
                stroke: new Stroke({
                    width: 5,
                    color: "#01a1fe",
                }),
            });
        };

        this.pivotsCurrentPositionLayer_ = this.createVectorLayer();
        this.pivotsCurrentPositionLayer_.setProperties({
            id: MapLayersIdEnum.PivotsCurrentPositionLayerId,
            name: "Pivots position",
            disable: false,
            minZoom: 15,
            visibleInMapTools: true,
            persistable: true,
            forceHidden: false,
            isSystem: true,
        });

        this.pivotsCurrentPositionLayer_.setStyle(style);
        this.addLayer(this.pivotsCurrentPositionLayer_);
    }

    private initIrrigationTasksPositionLayer() {
        const styleCallback = (feature: Feature) => {
            return new Style({
                stroke: new Stroke({
                    width: 4,
                    color: feature.getProperties()?.color,
                }),
                fill: new Fill({
                    color: feature.getProperties()?.color + "50",
                }),
            });
        };

        this.irrigationTasksPositionLayer_ = this.createVectorLayer();
        this.irrigationTasksPositionLayer_.setProperties({
            id: MapLayersIdEnum.IrrigationTasksPositionLayerId,
            name: "Irrigation tasks",
            disable: false,
            minZoom: 15,
            groupName: LayerGroupsEnum.IrrigationTaskPositions,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        this.irrigationTasksPositionLayer_.setStyle(styleCallback);
    }

    private irrigationTasksSegmentsLayer() {
        const styleCallback = (feature: Feature) => {
            return new Style({
                stroke: new Stroke({
                    width: 4,
                    color: feature.getProperties()?.color,
                }),
                fill: new Fill({
                    color: feature.getProperties()?.color + "50",
                }),
            });
        };

        this.irrigationTasksSegmentPositionLayer_ = this.createVectorLayer();
        this.irrigationTasksSegmentPositionLayer_.setProperties({
            id: MapLayersIdEnum.IrrigationTasksSegmentsLayerId,
            name: "Irrigation segments",
            disable: false,
            groupName: LayerGroupsEnum.IrrigationTaskPositions,
            minZoom: 15,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        this.irrigationTasksSegmentPositionLayer_.setStyle(styleCallback);
    }

    private initIrrigationTasksTransportationLayer() {
        const style = (feature: Feature) => {
            const geometry = feature.getGeometry() as LineString;
            const start = geometry.getFirstCoordinate();
            const end = geometry.getLastCoordinate();
            const dx = end[0] - start[0];
            const dy = end[1] - start[1];
            const rotation = Math.atan2(dy, dx);

            return [
                new Style({
                    stroke: new Stroke({
                        color: `${IrrigationEventColorsEnum.Transportation}`,
                        width: 4,
                    }),
                }),
                new Style({
                    geometry: new Point(end),
                    image: new Icon({
                        src: "assets/images/icons/arrow.png",
                        anchor: [1.8, 0.5],
                        rotateWithView: true,
                        rotation: -rotation,
                    }),
                }),
            ];
        };

        this.irrigationTasksTransportationLayer_ = this.createVectorLayer();
        this.irrigationTasksTransportationLayer_.setProperties({
            id: MapLayersIdEnum.IrrigationTasksTransportationLayerId,
            name: "Irrigation transportation",
            disable: false,
            groupName: LayerGroupsEnum.IrrigationTaskPositions,
            minZoom: 15,
        });
        this.irrigationTasksTransportationLayer_.setStyle(style);
    }

    private initMachinesOffLayer() {
        this.machinesOffLayer_ = this.createMachinesVectorLayer();
        this.machinesOffLayer_.setProperties({
            id: MapLayersIdEnum.MachinesOffLayerId,
            name: "Machines off",
            disable: false,
            groupName: MapPopupEnum.Machines,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initMachinesIdleLayer() {
        this.machinesIdleLayer_ = this.createMachinesVectorLayer();
        this.machinesIdleLayer_.setProperties({
            id: MapLayersIdEnum.MachinesIdleLayerId,
            name: "Machines idle",
            disable: false,
            groupName: MapPopupEnum.Machines,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initMachinesMovingLayer() {
        this.machinesMovingLayer_ = this.createMachinesVectorLayer();
        this.machinesMovingLayer_.setProperties({
            id: MapLayersIdEnum.MachinesMovingLayerId,
            name: "Machines moving",
            disable: false,
            groupName: MapPopupEnum.Machines,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initMachinesOfflineLayer() {
        this.machinesOfflineLayer_ = this.createMachinesVectorLayer();
        this.machinesOfflineLayer_.setProperties({
            id: MapLayersIdEnum.MachinesOfflineLayerId,
            name: "Machines offline",
            disable: false,
            groupName: MapPopupEnum.Machines,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initMachinesTrackLayer() {
        this.machinesTrackLayer_ = this.createVectorImageLayer();
        this.machinesTrackLayer_.setProperties({
            id: MapLayersIdEnum.MachinesTrackLayerId,
            name: "Machines track",
            disable: false,
            groupName: MapPopupEnum.Machines,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
    }

    private initMachinesEventsTrackLayer() {
        const layerStyles = () => {
            return new Style({
                stroke: new Stroke({
                    width: 3,
                    color: "#5769CC",
                }),
            });
        };

        this.machinesEventsTrackLayer_ = this.createVectorLayer();
        this.machinesEventsTrackLayer_.setProperties({
            id: MapLayersIdEnum.MachinesEventsTrackLayerId,
            name: "Machines events track",
            disable: false,
            groupName: MapPopupEnum.Machines,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        this.machinesEventsTrackLayer_.setStyle(layerStyles);
    }

    private initMachinesEventsWorkAreaLayer() {
        const layerStyles = () => {
            return new Style({
                fill: new Fill({
                    color: "rgba(114, 198, 47, 0.7)",
                }),
            });
        };

        this.machinesEventsWorkAreaLayer_ = this.createVectorLayer();
        this.machinesEventsWorkAreaLayer_.setProperties({
            id: MapLayersIdEnum.MachinesEventsWorkAreaId,
            name: "Machines events worked area",
            disable: false,
            groupName: MapPopupEnum.Machines,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        this.machinesEventsWorkAreaLayer_.setStyle(layerStyles);
    }

    private initIrrigationTasksLayer() {
        const layers = [
            this.irrigationTasksPositionLayer_,
            this.irrigationTasksSegmentPositionLayer_,
            this.irrigationTasksTransportationLayer_,
        ];
        this.irrigationTasksLayer_ = this.createLayerGroup(layers);
        this.irrigationTasksLayer_.setProperties({
            id: MapLayersIdEnum.IrrigationTasksLayerId,
            name: "Irrigation tasks",
            disable: false,
            visibleInMapTools: true,
            persistable: true,
            forceHidden: false,
            isSystem: true,
        });
        this.addLayer(this.irrigationTasksLayer_);
    }

    private initMachinesLayer() {
        const layers = [
            this.machinesEventsWorkAreaLayer_,
            this.machinesTrackLayer_,
            this.machinesOfflineLayer_,
            this.machinesOffLayer_,
            this.machinesIdleLayer_,
            this.machinesMovingLayer_,
            this.machinesEventsTrackLayer_,
        ];
        this.machinesLayer_ = this.createLayerGroup(layers);
        this.machinesLayer_.setProperties({
            id: MapLayersIdEnum.MachinesLayerId,
            name: "Machines",
            disable: false,
            visibleInMapTools: true,
            persistable: true,
            forceHidden: false,
            isSystem: true,
        });
        this.addLayer(this.machinesLayer_);
    }

    private initSoilVraWmsLayer() {
        this.soilVraWmsLayer_ = this.createWmsLayer(
            this.wmsServer,
            {},
            this.authService.organizationExtent()
        );
        this.soilVraWmsLayer_.setProperties({
            id: MapLayersIdEnum.VraMapWmsLayerId,
            name: "Soil VRA WMS",
            disable: true,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
            hasChildWmsLayers: false,
        });
        this.addLayer(this.soilVraWmsLayer_);
    }

    private initSoilVraVectorLayer() {
        this.soilVraVectorLayer_ = this.createVectorLayer();
        this.soilVraVectorLayer_.setProperties({
            id: MapLayersIdEnum.VraMapVectorLayerId,
            name: "Soil VRA Vector",
            disable: true,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });

        this.soilVraVectorLayer_.setStyle(this.getSoilVraVectorStyles());

        this.addLayer(this.soilVraVectorLayer_);
    }

    private initGeolocationLayer() {
        const markerFeature = new Feature<Point>();
        const markerStyles = new Style({
            image: new Icon({
                src: "assets/images/icons/map/geolocation-marker-heading.png",
                rotation: 0,
            }),
        });

        this.geolocationLayer_ = this.createVectorLayer();
        this.geolocationLayer_.setProperties({
            id: MapLayersIdEnum.GeolocationLayerId,
            name: "Geolocation",
            disable: false,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        markerFeature.setStyle(markerStyles);
        this.geolocationLayer_.getSource().addFeature(markerFeature);
        this.addLayer(this.geolocationLayer_);
    }

    private initSelectedPlotLayer() {
        const styleCallback = (feature: Feature) => {
            return new Style({
                stroke: new Stroke({
                    width: 5,
                    color: "#e3c524",
                }),
                fill: new Fill({
                    color: "rgba(227,197,36,0.2)",
                }),
            });
        };

        this.selectedPlotLayer_ = this.createVectorLayer();
        this.selectedPlotLayer_.setProperties({
            id: MapLayersIdEnum.SelectedPlotLayerId,
            name: "Selected Plot",
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        this.selectedPlotLayer_.setStyle(styleCallback);
        this.addLayer(this.selectedPlotLayer_);
    }

    private createVectorLayer() {
        return new VectorLayer({
            source: new VectorSource({
                features: [],
            }),
            visible: false,
        });
    }

    private createVectorImageLayer() {
        return new VectorImage({
            source: new VectorSource({
                features: [],
            }),
            visible: false,
        });
    }

    private createMachinesVectorLayer() {
        return new VectorLayer({
            source: new VectorSource({
                features: [],
            }),
            visible: false,
            style: (feature: Feature) => {
                return this.getMachineStyles(feature);
            },
        });
    }

    private getMachineStyles(feature: Feature) {
        const properties = feature.getProperties();

        const status: MachineStateEnum = properties.status;
        const color = MachinesColorByStatus[status];
        const borderWidth = properties.selected ? 3 : 0;

        let svg = "";

        switch (properties.unit_type) {
            case MachineTypeEnum.Sprayer:
                svg = `<svg width="48" height="56" viewBox="0 0 48 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M46.5 24C46.5 11.5736 36.4264 1.5 24 1.5C11.5736 1.5 1.5 11.5736 1.5 24C1.5 33.3734 7.23125 41.4047 15.3755 44.7874L21.5915 53.2839C21.592 53.2845 21.5924 53.2851 21.5928 53.2857C21.871 53.6668 22.2361 53.9729 22.6541 54.1823C23.0724 54.3919 23.5339 54.5 24 54.5C24.4661 54.5 24.9276 54.3919 25.3459 54.1823L24.6739 52.8412L25.3459 54.1823C25.7639 53.9729 26.129 53.6669 26.4072 53.2857C26.4076 53.2851 26.408 53.2845 26.4085 53.2839L32.6245 44.7874C40.7688 41.4047 46.5 33.3734 46.5 24Z" fill="white" stroke="#FFB073" stroke-width="${borderWidth}"/>
                <path d="M41 24C41 14.6112 33.3888 7 24 7C14.6112 7 7 14.6112 7 24C7 33.3888 14.6112 41 24 41C33.3888 41 41 33.3888 41 24Z" fill="${color}"/>
                <path d="M20 22.4999C18.3023 22.1913 16.1472 21.854 13.6142 22.7587C13.2375 22.8932 13 23.2593 13 23.6593V28C13 28.5523 13.4477 29 14 29H15" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M17 20L18.2236 17.5528C18.393 17.214 18.7393 17 19.118 17H23.382C23.7607 17 24.107 17.214 24.2764 17.5528L25.5 20M30 24H33.5423C34.0712 24 34.5 24.4288 34.5 24.9577V24.9577C34.5 25.2942 34.3235 25.6059 34.035 25.779L29.5 28.5L28.7111 28.8944C28.5723 28.9639 28.4192 29 28.2639 29H28" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M22.5 29L20 29" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="17.5" cy="28.5" r="2.5" stroke="white" stroke-width="1.5"/>
                <circle cx="25.5" cy="28.5" r="2.5" stroke="white" stroke-width="1.5"/>
                <rect x="29" y="18" width="3" height="6" rx="1" stroke="white" stroke-width="1.5"/>
                <path d="M29 31V32.5" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                <path d="M32 30V31.5" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                <path d="M35 29V30.5" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                </svg>
                `;
                break;
            case MachineTypeEnum.Harvester:
                svg = `<svg width="48" height="56" viewBox="0 0 48 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M46.5 24C46.5 11.5736 36.4264 1.5 24 1.5C11.5736 1.5 1.5 11.5736 1.5 24C1.5 33.3734 7.23125 41.4047 15.3755 44.7874L21.5915 53.2839C21.592 53.2845 21.5924 53.2851 21.5928 53.2857C21.871 53.6668 22.2361 53.9729 22.6541 54.1823C23.0724 54.3919 23.5339 54.5 24 54.5C24.4661 54.5 24.9276 54.3919 25.3459 54.1823L24.6739 52.8412L25.3459 54.1823C25.7639 53.9729 26.129 53.6669 26.4072 53.2857C26.4076 53.2851 26.408 53.2845 26.4085 53.2839L32.6245 44.7874C40.7688 41.4047 46.5 33.3734 46.5 24Z" fill="white" stroke="#FFB073" stroke-width="${borderWidth}"/>
                <path d="M41 24C41 14.6112 33.3888 7 24 7C14.6112 7 7 14.6112 7 24C7 33.3888 14.6112 41 24 41C33.3888 41 41 33.3888 41 24Z" fill="${color}"/>
                <circle cx="16" cy="28" r="3" stroke="white" stroke-width="1.5"/>
                <circle cx="24" cy="28" r="3" stroke="white" stroke-width="1.5"/>
                <path d="M21.5 28H19" stroke="white" stroke-width="1.5"/>
                <path d="M13 28.0008V22.7652L14.0589 22.5423M19.7063 21.3534H23.5889M19.7063 21.3534V18C19.7063 16.8954 20.6018 16 21.7063 16H25.5842C26.1147 16 26.6264 16.2087 26.9948 16.5902C28.1176 17.7529 28.6872 18.5462 29.5 20.5M19.7063 21.3534L14.0589 22.5423M19.3534 18.1767H16.7061H15.0589C14.5066 18.1767 14.0589 18.6244 14.0589 19.1767V22.5423" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M34.3363 24.3884C35.0799 24.5876 35.3289 25.5171 34.7846 26.0614L31.4134 29.4326C30.8691 29.9769 29.9396 29.7279 29.7404 28.9843L28.5065 24.3792C28.3072 23.6357 28.9877 22.9552 29.7312 23.1545L34.3363 24.3884Z" stroke="white" stroke-width="1.5"/>
                </svg>
                `;
                break;
            case MachineTypeEnum.Car:
                svg = `<svg width="48" height="56" viewBox="0 0 48 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M46.5 24C46.5 11.5736 36.4264 1.5 24 1.5C11.5736 1.5 1.5 11.5736 1.5 24C1.5 33.3734 7.23125 41.4047 15.3755 44.7874L21.5915 53.2839C21.592 53.2845 21.5924 53.2851 21.5928 53.2857C21.871 53.6668 22.2361 53.9729 22.6541 54.1823C23.0724 54.3919 23.5339 54.5 24 54.5C24.4661 54.5 24.9276 54.3919 25.3459 54.1823L24.6739 52.8412L25.3459 54.1823C25.7639 53.9729 26.129 53.6669 26.4072 53.2857C26.4076 53.2851 26.408 53.2845 26.4085 53.2839L32.6245 44.7874C40.7688 41.4047 46.5 33.3734 46.5 24Z" fill="white" stroke="#FFB073" stroke-width="${borderWidth}"/>
                <path d="M41 24C41 14.6112 33.3888 7 24 7C14.6112 7 7 14.6112 7 24C7 33.3888 14.6112 41 24 41C33.3888 41 41 33.3888 41 24Z" fill="${color}"/>
                <path d="M16 27H15C13.9 27 13 26.1 13 25V23.07C13 22.4 13.33 21.78 13.89 21.41L16 20L17.41 18.59C17.79 18.21 18.29 18 18.82 18H26.16C26.69 18 27.2 18.21 27.57 18.59L30.98 22L33.47 22.62C34.36 22.84 34.98 23.64 34.98 24.56V26C34.98 26.55 34.53 27 33.98 27H32.98" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M29 27H20" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M31 29C32.1046 29 33 28.1046 33 27C33 25.8954 32.1046 25 31 25C29.8954 25 29 25.8954 29 27C29 28.1046 29.8954 29 31 29Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18 29C19.1046 29 20 28.1046 20 27C20 25.8954 19.1046 25 18 25C16.8954 25 16 25.8954 16 27C16 28.1046 16.8954 29 18 29Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M27 22H24" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                `;
                break;
            case MachineTypeEnum.Truck:
                svg = `<svg width="48" height="56" viewBox="0 0 48 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M46.5 24C46.5 11.5736 36.4264 1.5 24 1.5C11.5736 1.5 1.5 11.5736 1.5 24C1.5 33.3734 7.23125 41.4047 15.3755 44.7874L21.5915 53.2839C21.592 53.2845 21.5924 53.2851 21.5928 53.2857C21.871 53.6668 22.2361 53.9729 22.6541 54.1823C23.0724 54.3919 23.5339 54.5 24 54.5C24.4661 54.5 24.9276 54.3919 25.3459 54.1823L24.6739 52.8412L25.3459 54.1823C25.7639 53.9729 26.129 53.6669 26.4072 53.2857C26.4076 53.2851 26.408 53.2845 26.4085 53.2839L32.6245 44.7874C40.7688 41.4047 46.5 33.3734 46.5 24Z" fill="white" stroke="#FFB073" stroke-width="${borderWidth}"/>
                <path d="M41 24C41 14.6112 33.3888 7 24 7C14.6112 7 7 14.6112 7 24C7 33.3888 14.6112 41 24 41C33.3888 41 41 33.3888 41 24Z" fill="${color}"/>
                <path d="M27 21H30.38C30.76 21 31.11 21.21 31.27 21.55L32.99 25L34.44 25.72C34.78 25.89 34.99 26.24 34.99 26.61V29.99C34.99 30.54 34.54 30.99 33.99 30.99H32.99" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M29 31H27" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M31 33C32.1046 33 33 32.1046 33 31C33 29.8954 32.1046 29 31 29C29.8954 29 29 29.8954 29 31C29 32.1046 29.8954 33 31 33Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18 33C19.1046 33 20 32.1046 20 31C20 29.8954 19.1046 29 18 29C16.8954 29 16 29.8954 16 31C16 32.1046 16.8954 33 18 33Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M33 25H30" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M16 31H15C13.9 31 13 30.1 13 29V19C13 17.9 13.9 17 15 17H25C26.1 17 27 17.9 27 19V31H20" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                `;
                break;
            case MachineTypeEnum.Tractor:
                svg = `<svg width="48" height="56" viewBox="0 0 48 56" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M46.5 24C46.5 11.5736 36.4264 1.5 24 1.5C11.5736 1.5 1.5 11.5736 1.5 24C1.5 33.3734 7.23125 41.4047 15.3755 44.7874L21.5915 53.2839C21.592 53.2845 21.5924 53.2851 21.5928 53.2857C21.871 53.6668 22.2361 53.9729 22.6541 54.1823C23.0724 54.3919 23.5339 54.5 24 54.5C24.4661 54.5 24.9276 54.3919 25.3459 54.1823L24.6739 52.8412L25.3459 54.1823C25.7639 53.9729 26.129 53.6669 26.4072 53.2857C26.4076 53.2851 26.408 53.2845 26.4085 53.2839L32.6245 44.7874C40.7688 41.4047 46.5 33.3734 46.5 24Z" fill="white" stroke="#FFB073" stroke-width="${borderWidth}"/>
                <path d="M41 24C41 14.6112 33.3888 7 24 7C14.6112 7 7 14.6112 7 24C7 33.3888 14.6112 41 24 41C33.3888 41 41 33.3888 41 24Z" fill="${color}"/>
                <path d="M23.384 17.2447L22.6581 17.4335V17.4335L23.384 17.2447ZM15 14.25C14.5858 14.25 14.25 14.5858 14.25 15C14.25 15.4142 14.5858 15.75 15 15.75V14.25ZM22.6581 17.4335L24.0029 22.6026L25.4546 22.225L24.1098 17.0558L22.6581 17.4335ZM24.7288 23.1638H30.6V21.6638H24.7288V23.1638ZM30.6 23.1638H31V21.6638H30.6V23.1638ZM33.25 25.4138V26.5H34.75V25.4138H33.25ZM29.5 28.25H23.5V29.75H29.5V28.25ZM15 15.75H17.411V14.25H15V15.75ZM17.411 15.75H20.4806V14.25H17.411V15.75ZM16.6697 14.8857L15.4642 22.7018L16.9467 22.9304L18.1522 15.1143L16.6697 14.8857ZM24.7288 21.6638H21V23.1638H24.7288V21.6638ZM31.35 22.4138V19.5402H29.85V22.4138H31.35ZM31 23.1638C32.2426 23.1638 33.25 24.1712 33.25 25.4138H34.75C34.75 23.3427 33.0711 21.6638 31 21.6638V23.1638ZM31.35 19.5402C31.35 17.7612 29.9078 16.319 28.1288 16.319V17.819C29.0794 17.819 29.85 18.5896 29.85 19.5402H31.35ZM24.1098 17.0558C23.6799 15.4034 22.188 14.25 20.4806 14.25V15.75C21.5051 15.75 22.4002 16.442 22.6581 17.4335L24.1098 17.0558ZM23.25 27C23.25 29.3472 21.3472 31.25 19 31.25V32.75C22.1756 32.75 24.75 30.1756 24.75 27H23.25ZM19 31.25C16.6528 31.25 14.75 29.3472 14.75 27H13.25C13.25 30.1756 15.8244 32.75 19 32.75V31.25ZM14.75 27C14.75 24.6528 16.6528 22.75 19 22.75V21.25C15.8244 21.25 13.25 23.8244 13.25 27H14.75ZM19 22.75C21.3472 22.75 23.25 24.6528 23.25 27H24.75C24.75 23.8244 22.1756 21.25 19 21.25V22.75ZM34.25 29C34.25 30.2426 33.2426 31.25 32 31.25V32.75C34.0711 32.75 35.75 31.0711 35.75 29H34.25ZM32 31.25C30.7574 31.25 29.75 30.2426 29.75 29H28.25C28.25 31.0711 29.9289 32.75 32 32.75V31.25ZM29.75 29C29.75 27.7574 30.7574 26.75 32 26.75V25.25C29.9289 25.25 28.25 26.9289 28.25 29H29.75ZM32 26.75C33.2426 26.75 34.25 27.7574 34.25 29H35.75C35.75 26.9289 34.0711 25.25 32 25.25V26.75Z" fill="white"/>
                </svg>
                `;
                break;
        }

        return new Style({
            image: new Icon({
                src: "data:image/svg+xml;utf8," + escape(svg),
            }),
        });
    }

    private getSoilVraVectorStyles() {
        return (feature: Feature, resolution: number) => {
            return new Style({
                stroke: new Stroke({
                    color: "#C90000",
                    width: 2,
                }),
                text: new Text({
                    font: "12px sans-serif",
                    fill: new Fill({ color: "#000000" }),
                    stroke: new Stroke({
                        color: "#FFFFFF",
                        width: 3,
                    }),
                    text: ((f: Feature, r: number) => {
                        let text = f.getProperties().DN + "";
                        if (r > 20) {
                            text = "";
                        }
                        return text;
                    })(feature, resolution),
                }),
            });
        };
    }

    private createLayerGroup(layers: BaseLayer[]) {
        return new LayerGroup({
            layers,
            visible: false,
        });
    }

    private createClusterVectorLayer() {
        const vectorSource = new VectorSource({
            features: [],
        });

        const clusterSource = new Cluster({
            source: vectorSource,
            distance: 80,
        });

        return new VectorLayer({
            source: clusterSource,
            visible: false,
            style: (feature: Feature) => {
                return this.getPlatformStyles(feature);
            },
        });
    }

    private getPlatformStyles(feature: Feature) {
        const [firstElementOfFeatureCluster] = feature.get("features");
        const clusterFeaturesProp =
            firstElementOfFeatureCluster.getProperties();

        const clusterFeaturesState: IrrigationPlatformsEventEnum =
            clusterFeaturesProp.unit
                ? clusterFeaturesProp.unit.state
                : clusterFeaturesProp.state;

        const hasSelectedPlatformInCluster = feature
            .get("features")
            .some((feature: Feature) => feature.getProperties().selected);

        const borderWidth = hasSelectedPlatformInCluster ? 10 : 0;
        const color = IrrigationPlatformsColorByEvent[clusterFeaturesState];
        /* eslint-disable max-len */
        const svg = `<svg width="47" height="54" viewBox="0 0 47 54" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M18.6925 43.2561C18.225 42.7135 17.5489 42.4022 16.838 42.4022H9.92308C7.20414 42.4022 5 40.1695 5 37.4152V9.98696C5 7.23274 7.20414 5 9.92308 5H37C39.7189 5 41.9231 7.23274 41.9231 9.98696V37.4152C41.9231 40.1695 39.7189 42.4022 37 42.4022H28.8543C28.1434 42.4022 27.4673 42.7135 26.9998 43.2561L22.8462 48.0769L18.6925 43.2561Z" stroke="#FFB073" stroke-width="${borderWidth}" stroke-linejoin="round"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M18.6925 43.2561C18.225 42.7135 17.5489 42.4022 16.838 42.4022H9.92308C7.20414 42.4022 5 40.1695 5 37.4152V9.98696C5 7.23274 7.20414 5 9.92308 5H37C39.7189 5 41.9231 7.23274 41.9231 9.98696V37.4152C41.9231 40.1695 39.7189 42.4022 37 42.4022H28.8543C28.1434 42.4022 27.4673 42.7135 26.9998 43.2561L22.8462 48.0769L18.6925 43.2561Z" fill="${color}" stroke="white" stroke-width="3" stroke-linejoin="round"/>
        <rect x="20.5386" y="23.5386" width="6" height="3" rx="1.5" stroke="white" stroke-width="1.5"/>
        <path d="M19.5386 28.5386C19.5386 27.434 20.434 26.5386 21.5386 26.5386H25.5386C26.6431 26.5386 27.5386 27.434 27.5386 28.5386V34.5386H19.5386V28.5386Z" stroke="white" stroke-width="1.5"/>
        <circle cx="23.5386" cy="13.5386" r="1" fill="white"/>
        <ellipse cx="23.5386" cy="19.5386" rx="1" ry="1" fill="white"/>
        <circle cx="33.1008" cy="15.0384" r="1" transform="rotate(60 33.1008 15.0384)" fill="white"/>
        <circle cx="1" cy="1" r="1" transform="matrix(-0.5 0.866025 0.866025 0.5 13.5386 13.6724)" fill="white"/>
        <ellipse cx="27.9045" cy="18.0384" rx="1" ry="1" transform="rotate(60 27.9045 18.0384)" fill="white"/>
        <ellipse cx="1" cy="1" rx="1" ry="1" transform="matrix(-0.5 0.866025 0.866025 0.5 18.7349 16.6724)" fill="white"/>
        <path d="M33.5386 34.5386H13.5386" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
        </svg>`;

        const itemsInCluster = feature.get("features")?.length;
        let style = this.platformsStyleCache[color + borderWidth];

        if (!style) {
            style = this.platformsStyleCache[color + borderWidth] = new Style({
                image: new Icon({
                    src: "data:image/svg+xml;utf8," + escape(svg),
                }),
            });
        }

        if (itemsInCluster > 1) {
            const count = new Style({
                image: new RegularShape({
                    points: 12,
                    radius: 12,
                    displacement: [22, 22],
                    fill: new Fill({
                        color,
                    }),
                    stroke: new Stroke({
                        color: "#fff",
                        width: 2,
                    }),
                }),
                text: new Text({
                    text: itemsInCluster.toString(),
                    font: "10px Montserrat-Medium",
                    offsetX: 22,
                    offsetY: -22,
                    fill: new Fill({
                        color: "#fff",
                    }),
                }),
                zIndex: 21,
            });

            return [style, count];
        }

        return [style];
    }

    private initSurveysLayer() {
        this.surveysLayer_ = this.createVectorLayer();
        this.surveysLayer_.setProperties({
            id: MapLayersIdEnum.SurveysLayerId,
            name: "Surveys",
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        this.surveysLayer_.setStyle(this.getSurveysLayerStyles());
        this.addLayer(this.surveysLayer_);
    }

    private getSurveysLayerStyles() {
        return (feature: Feature) => {
            return new Style({
                stroke: new Stroke({
                    width: 2,
                    color: "rgb(114, 198, 47)",
                }),
                fill: new Fill({
                    color: "rgba(255, 255, 255, 0.3)",
                }),
                text: new Text({
                    font: "12px sans-serif",
                    fill: new Fill({ color: "#000000" }),
                    text: ((f: Feature) => {
                        return f.getProperties().plot_name;
                    })(feature),
                }),
            });
        };
    }

    private initKvsManageConractsLayer() {
        this.kvsManageConractsLayer_ = this.createVectorLayer();
        this.kvsManageConractsLayer_.setProperties({
            id: MapLayersIdEnum.KvsManageContractsLayerId,
            name: "KVS Manage Contracts",
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
            visible: false,
        });

        this.addLayer(this.kvsManageConractsLayer_);
    }

    private initMeasurementLayer(): void {
        const layerStyles = () => {
            return new Style({
                fill: new Fill({
                    color: "rgba(255, 255, 255, 0.2)",
                }),
                stroke: new Stroke({
                    color: "#ffcc33",
                    width: 2,
                }),
                image: new CircleStyle({
                    radius: 7,
                    fill: new Fill({
                        color: "#ffcc33",
                    }),
                }),
            });
        };

        this.measurementLayer_ = this.createVectorLayer();
        this.measurementLayer_.setProperties({
            id: MapLayersIdEnum.MeasurementLayerId,
            disable: false,
            visibleInMapTools: false,
            persistable: false,
            forceHidden: false,
        });
        this.measurementLayer_.set("name", MapLayersIdEnum.MeasurementLayerId);

        this.measurementLayer_.setStyle(layerStyles);
        this.addLayer(this.measurementLayer_);
        this.addMeasurementInteraction(this.measurementLayer_, "LineString");
        this.addMeasurementInteraction(this.measurementLayer_, "Polygon");
    }

    private addMeasurementInteraction(
        layer: VectorLayer,
        type: GeometryType
    ): void {
        const draw = new Draw({
            source: layer.getSource(),
            type: type,
            style: new Style({
                fill: new Fill({
                    color: "rgba(255, 255, 255, 0.2)",
                }),
                stroke: new Stroke({
                    color: "rgba(0, 0, 0, 0.5)",
                    lineDash: [10, 10],
                    width: 2,
                }),
                image: new CircleStyle({
                    radius: 5,
                    stroke: new Stroke({
                        color: "rgba(0, 0, 0, 0.7)",
                    }),
                    fill: new Fill({
                        color: "rgba(255, 255, 255, 0.2)",
                    }),
                }),
            }),
        });

        this.addInteraction(draw);
        draw.setActive(false);
        draw.set("type", type);
    }

    private measurementStart(e: DrawEvent) {
        const measurementLayer = this.getLayerByName(
            MapLayersIdEnum.MeasurementLayerId
        ) as VectorLayer;
        measurementLayer.getSource().clear();

        this.map
            .getOverlayById(MapLayersIdEnum.MeasurementLayerId)
            .getElement()
            .parentElement.classList.remove("visible");

        this.measurementToolTip.setFeature(e.feature);
        const sketch = e.feature;

        this.mapDrawChangeEventKey = sketch
            .getGeometry()
            .on("change", (evt) => {
                const geom = evt.target;
                let tooltipCoord;
                if (geom instanceof Polygon) {
                    tooltipCoord = geom.getInteriorPoint().getCoordinates();
                } else if (geom instanceof LineString) {
                    tooltipCoord = geom.getLastCoordinate();
                }
                this.measurementCoordinates = tooltipCoord;
            });
    }

    private measurementEnd(e: DrawEvent): void {
        this.measurementToolTip.removeFeature();
        this.measurementToolTip.setPosition(this.measurementCoordinates);

        const measureDetailsPopup = this.map.getOverlayById(
            MapLayersIdEnum.MeasurementLayerId
        );

        unByKey(this.mapDrawChangeEventKey);

        setTimeout(() => {
            measureDetailsPopup.setPosition(this.measurementCoordinates);
            measureDetailsPopup
                .getElement()
                .parentElement.classList.add("visible");
            measureDetailsPopup.getElement().innerHTML =
                this.measurementToolTip.prevHTML;
        }, 0.5);
    }

    private setCursorPointer(event: MapBrowserEvent<PointerEvent>) {
        const hasFeature = this.map.hasFeatureAtPixel(event.pixel, {
            layerFilter: (layer) => {
                const layerGroup = layer.getProperties().groupName;
                const layerId = layer.getProperties().id;

                return (
                    [MapPopupEnum.Machines, MapPopupEnum.Platforms].includes(
                        layerGroup
                    ) ||
                    [
                        MapLayersIdEnum.WeatherStationsLayerId,
                        MapLayersIdEnum.MarkersLayerId,
                    ].includes(layerId) ||
                    !!this.technofarmHoveredFeaturesIds.length
                );
            },
        });

        this.map.getTargetElement().style.cursor = hasFeature ? "pointer" : "";
    }

    private highlightTechnofarmFeature(
        event: MapBrowserEvent<PointerEvent>,
        minZoomLevel = 14
    ) {
        this.technofarmHoveredFeaturesIds = [];

        const zoomLevel = event.map?.getView()?.getZoom() ?? 0;

        this.map.forEachFeatureAtPixel(
            event.pixel,
            (feature) => {
                const f = toFeature(feature as RenderFeature);
                if (
                    !f.getGeometry().intersectsCoordinate(event.coordinate) ||
                    zoomLevel < minZoomLevel
                ) {
                    return;
                }

                const layerFeatureId =
                    f.get("layer_id") + "_" + feature.getId();

                this.technofarmHoveredFeaturesIds.push(layerFeatureId);
            },
            {
                layerFilter: (layer) =>
                    [
                        MapLayersIdEnum.TechnofarmLayerId,
                        MapLayersIdEnum.TechnofarmMapcacheLayerId,
                    ].includes(layer.get("id")),
            }
        );

        this.technofarmLayer_.changed();
        this.technofarmMapcacheLayer_.changed();
    }

    private getColorFromRange(
        range: { [key: string]: string },
        value: number
    ): ColorLike {
        for (const key in range) {
            const [min, max] = key.split("-").map(Number);
            if (value >= min && value < max) {
                return range[key];
            }
        }
        return "rgb(255, 0, 0)"; // Default color if no value matches
    }

    private getHiddenFillOptionsByStyleLayerId(
        childLayers: MapLayerType<TFMapLayerAttributesType>[]
    ) {
        return childLayers.reduce(
            (
                acc: {
                    [key: string]: {
                        fillColumnName: string;
                        hiddenValues: string[];
                    };
                },
                childLayer
            ) => {
                const styleLayerId = childLayer.attributes?.style?.layer_id;
                const fillColumnName =
                    childLayer.attributes?.style?.fill_column_name;
                const hiddenFillValues =
                    childLayer.attributes?.style?.hidden_fill_values ?? [];

                if (
                    styleLayerId &&
                    fillColumnName &&
                    hiddenFillValues.length > 0
                ) {
                    acc[styleLayerId] = {
                        fillColumnName,
                        hiddenValues: hiddenFillValues,
                    };
                }

                return acc;
            },
            {}
        );
    }
}
